@echo off
echo 🧹 <PERSON>IM<PERSON>ANDO DEPENDENCIAS Y MIGRANDO A SUPABASE...

REM Limpiar node_modules y package-lock
echo 📦 Limpiando node_modules...
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json

REM Limpiar caché de npm
echo 🗑️ Limpiando caché de npm...
npm cache clean --force

REM Limpiar caché de Expo
echo 📱 Limpiando caché de Expo...
npx expo r -c

REM Reinstalar dependencias
echo ⬇️ Reinstalando dependencias...
npm install

echo ✅ MIGRACIÓN A SUPABASE COMPLETADA!
echo.
echo 📋 PRÓXIMOS PASOS:
echo 1. Ejecutar: npm start
echo 2. Probar autenticación
echo 3. Verificar que todas las funciones usen Supabase
echo.
echo 🚀 LA APP ANDROID AHORA USA 100%% SUPABASE!
pause
