'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/supabase-auth-context';
import { useSupabaseSubscription } from '@/contexts/supabase-subscription-context';
import { checkUserLimits, getLimitMessage } from '@/lib/gamification';

interface Article {
  id: string;
  user_id: string;
  title: string;
  content: string;
  topic?: string;
  saved_at: string;
  created_at: string;
}

interface Recommendation {
  id: string;
  user_id: string;
  title: string;
  content: string;
  type?: string;
  generated_from?: string;
  metadata?: Record<string, any>;
  created_at: string;
}

interface UseContentReturn {
  // Articles
  articles: Article[];
  articlesLoading: boolean;
  articlesError: string | null;
  saveArticle: (title: string, content: string, topic?: string) => Promise<Article | null>;
  deleteArticle: (id: string) => Promise<boolean>;
  generateArticle: (topic: string) => Promise<Article | null>;
  
  // Recommendations
  recommendations: Recommendation[];
  recommendationsLoading: boolean;
  recommendationsError: string | null;
  saveRecommendation: (title: string, content: string, type?: string, generatedFrom?: string, metadata?: Record<string, any>) => Promise<Recommendation | null>;
  deleteRecommendation: (id: string) => Promise<boolean>;
  generateRecommendations: () => Promise<Recommendation[]>;
  
  // Common
  refreshData: () => Promise<void>;
  clearErrors: () => void;
}

export const useSupabaseContent = (): UseContentReturn => {
  const { user, userProfile } = useAuth();
  const { isSubscribed } = useSupabaseSubscription();
  
  // Articles state
  const [articles, setArticles] = useState<Article[]>([]);
  const [articlesLoading, setArticlesLoading] = useState(true);
  const [articlesError, setArticlesError] = useState<string | null>(null);
  
  // Recommendations state
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [recommendationsLoading, setRecommendationsLoading] = useState(true);
  const [recommendationsError, setRecommendationsError] = useState<string | null>(null);

  // =================== ARTICLES ===================

  const loadArticles = useCallback(async () => {
    if (!user) return;

    try {
      setArticlesLoading(true);
      const { data, error } = await supabase
        .from('articles')
        .select('*')
        .eq('user_id', user.id)
        .order('saved_at', { ascending: false });

      if (error) {
        console.error('Error loading articles:', error);
        setArticlesError('Error cargando artículos');
        return;
      }

      setArticles(data ?? []);
    } catch (error) {
      console.error('Error loading articles:', error);
      setArticlesError('Error cargando artículos');
    } finally {
      setArticlesLoading(false);
    }
  }, [user]);

  const saveArticle = useCallback(async (
    title: string,
    content: string,
    topic?: string
  ): Promise<Article | null> => {
    if (!user) return null;

    try {
      const { data, error } = await supabase
        .from('articles')
        .insert({
          user_id: user.id,
          title,
          content,
          topic,
        })
        .select()
        .single();

      if (error) {
        console.error('Error saving article:', error);
        setArticlesError('Error guardando artículo');
        return null;
      }

      setArticles(prev => [data, ...prev]);
      return data;
    } catch (error) {
      console.error('Error saving article:', error);
      setArticlesError('Error guardando artículo');
      return null;
    }
  }, [user]);

  const deleteArticle = useCallback(async (id: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('articles')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting article:', error);
        setArticlesError('Error eliminando artículo');
        return false;
      }

      setArticles(prev => prev.filter(article => article.id !== id));
      return true;
    } catch (error) {
      console.error('Error deleting article:', error);
      setArticlesError('Error eliminando artículo');
      return false;
    }
  }, []);

  const generateArticle = useCallback(async (topic: string): Promise<Article | null> => {
    if (!user) return null;

    // 🚨 VERIFICAR LÍMITES ANTES DE GENERAR ARTÍCULO
    const limits = checkUserLimits(userProfile, isSubscribed);
    
    if (!limits.canGenerateArticle) {
      const limitMessage = getLimitMessage('articles', isSubscribed);
      setArticlesError(limitMessage ?? 'Límite de artículos alcanzado');
      throw new Error(limitMessage ?? 'Límite de artículos mensuales alcanzado');
    }

    try {
      // Llamar a la API de generación de artículos (si existe)
      const response = await fetch('/api/generate-article', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          uid: user.id,
          topic,
        }),
      });

      if (!response.ok) {
        throw new Error('Error en la API de generación de artículos');
      }

      const articleData = await response.json();

      // Guardar el artículo generado
      return await saveArticle(
        articleData.title ?? `Artículo sobre ${topic}`,
        articleData.content ?? articleData.article ?? 'Contenido no disponible',
        topic
      );
    } catch (error) {
      console.error('Error generating article:', error);
      setArticlesError('Error generando artículo');
      return null;
    }
  }, [user, saveArticle, userProfile, isSubscribed]);

  // =================== RECOMMENDATIONS ===================

  const loadRecommendations = useCallback(async () => {
    if (!user) return;

    try {
      setRecommendationsLoading(true);
      const { data, error } = await supabase
        .from('recommendations')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading recommendations:', error);
        setRecommendationsError('Error cargando recomendaciones');
        return;
      }

      setRecommendations(data ?? []);
    } catch (error) {
      console.error('Error loading recommendations:', error);
      setRecommendationsError('Error cargando recomendaciones');
    } finally {
      setRecommendationsLoading(false);
    }
  }, [user]);

  const saveRecommendation = useCallback(async (
    title: string,
    content: string,
    type?: string,
    generatedFrom?: string,
    metadata?: Record<string, any>
  ): Promise<Recommendation | null> => {
    if (!user) return null;

    try {
      const { data, error } = await supabase
        .from('recommendations')
        .insert({
          user_id: user.id,
          title,
          content,
          type,
          generated_from: generatedFrom,
          metadata,
        })
        .select()
        .single();

      if (error) {
        console.error('Error saving recommendation:', error);
        setRecommendationsError('Error guardando recomendación');
        return null;
      }

      setRecommendations(prev => [data, ...prev]);
      return data;
    } catch (error) {
      console.error('Error saving recommendation:', error);
      setRecommendationsError('Error guardando recomendación');
      return null;
    }
  }, [user]);

  const deleteRecommendation = useCallback(async (id: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from('recommendations')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting recommendation:', error);
        setRecommendationsError('Error eliminando recomendación');
        return false;
      }

      setRecommendations(prev => prev.filter(rec => rec.id !== id));
      return true;
    } catch (error) {
      console.error('Error deleting recommendation:', error);
      setRecommendationsError('Error eliminando recomendación');
      return false;
    }
  }, []);

  const generateRecommendations = useCallback(async (): Promise<Recommendation[]> => {
    if (!user) return [];

    // 🚨 VERIFICAR LÍMITES ANTES DE GENERAR RECOMENDACIONES
    const limits = checkUserLimits(userProfile, isSubscribed);
    
    if (!limits.canGenerateRecommendation) {
      const limitMessage = getLimitMessage('recommendations', isSubscribed);
      setRecommendationsError(limitMessage ?? 'Límite de recomendaciones alcanzado');
      throw new Error(limitMessage ?? 'Límite de recomendaciones diarias alcanzado');
    }

    try {
      // Llamar a la API de recomendaciones
      const response = await fetch('/api/recommendations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          uid: user.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Error en la API de recomendaciones');
      }

      const recommendationsData = await response.json();

      // Guardar las recomendaciones generadas
      const savedRecommendations: Recommendation[] = [];
      
      if (Array.isArray(recommendationsData.recommendations)) {
        for (const rec of recommendationsData.recommendations) {
          const saved = await saveRecommendation(
            rec.title ?? 'Recomendación personalizada',
            rec.content ?? rec.recommendation ?? 'Contenido no disponible',
            rec.type ?? 'general',
            'ai_generated',
            rec.metadata ?? {}
          );
          if (saved) {
            savedRecommendations.push(saved);
          }
        }
      } else if (recommendationsData.recommendation) {
        // Si es una sola recomendación
        const saved = await saveRecommendation(
          recommendationsData.title ?? 'Recomendación personalizada',
          recommendationsData.recommendation,
          recommendationsData.type ?? 'general',
          'ai_generated',
          recommendationsData.metadata ?? {}
        );
        if (saved) {
          savedRecommendations.push(saved);
        }
      }

      return savedRecommendations;
    } catch (error) {
      console.error('Error generating recommendations:', error);
      setRecommendationsError('Error generando recomendaciones');
      return [];
    }
  }, [user, saveRecommendation, userProfile, isSubscribed]);

  // =================== COMMON FUNCTIONS ===================

  // Helper function to handle article changes
  const handleArticleChange = useCallback((payload: any) => {
    if (payload.eventType === 'INSERT') {
      const newArticle = payload.new as Article;
      setArticles(prev => {
        const exists = prev.some(article => article.id === newArticle.id);
        if (exists) return prev;
        return [newArticle, ...prev];
      });
    } else if (payload.eventType === 'DELETE') {
      setArticles(prev => prev.filter(article => article.id !== payload.old.id));
    }
  }, []);

  // Helper function to handle recommendation changes
  const handleRecommendationChange = useCallback((payload: any) => {
    if (payload.eventType === 'INSERT') {
      const newRecommendation = payload.new as Recommendation;
      setRecommendations(prev => {
        const exists = prev.some(rec => rec.id === newRecommendation.id);
        if (exists) return prev;
        return [newRecommendation, ...prev];
      });
    } else if (payload.eventType === 'DELETE') {
      setRecommendations(prev => prev.filter(rec => rec.id !== payload.old.id));
    }
  }, []);

  const refreshData = useCallback(async () => {
    await Promise.all([loadArticles(), loadRecommendations()]);
  }, [loadArticles, loadRecommendations]);

  const clearErrors = useCallback(() => {
    setArticlesError(null);
    setRecommendationsError(null);
  }, []);

  // =================== EFFECTS ===================

  // Cargar datos iniciales
  useEffect(() => {
    if (user) {
      refreshData();
    } else {
      setArticles([]);
      setRecommendations([]);
      setArticlesLoading(false);
      setRecommendationsLoading(false);
    }
  }, [user, refreshData]);

  // Suscribirse a cambios en tiempo real para artículos
  useEffect(() => {
    if (!user) return;

    const articlesChannel = supabase
      .channel(`articles_${user.id}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'articles',
        filter: `user_id=eq.${user.id}`,
      }, handleArticleChange)
      .subscribe();

    return () => {
      articlesChannel.unsubscribe();
    };
  }, [user, handleArticleChange]);

  // Suscribirse a cambios en tiempo real para recomendaciones
  useEffect(() => {
    if (!user) return;

    const recommendationsChannel = supabase
      .channel(`recommendations_${user.id}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'recommendations',
        filter: `user_id=eq.${user.id}`,
      }, handleRecommendationChange)
      .subscribe();

    return () => {
      recommendationsChannel.unsubscribe();
    };
  }, [user, handleRecommendationChange]);

  return useMemo(() => ({
    // Articles
    articles,
    articlesLoading,
    articlesError,
    saveArticle,
    deleteArticle,
    generateArticle,
    
    // Recommendations
    recommendations,
    recommendationsLoading,
    recommendationsError,
    saveRecommendation,
    deleteRecommendation,
    generateRecommendations,
    
    // Common
    refreshData,
    clearErrors,
  }), [
    articles,
    articlesLoading,
    articlesError,
    saveArticle,
    deleteArticle,
    generateArticle,
    recommendations,
    recommendationsLoading,
    recommendationsError,
    saveRecommendation,
    deleteRecommendation,
    generateRecommendations,
    refreshData,
    clearErrors,
  ]);
};
