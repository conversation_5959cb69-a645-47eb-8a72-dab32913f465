package com.menteencalma.app.di;

import com.google.firebase.functions.FirebaseFunctions;
import com.menteencalma.app.data.service.CloudFunctionsService;
import com.menteencalma.app.data.service.MockCloudFunctionsService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideCloudFunctionsServiceFactory implements Factory<CloudFunctionsService> {
  private final Provider<FirebaseFunctions> functionsProvider;

  private final Provider<MockCloudFunctionsService> mockServiceProvider;

  public AppModule_ProvideCloudFunctionsServiceFactory(
      Provider<FirebaseFunctions> functionsProvider,
      Provider<MockCloudFunctionsService> mockServiceProvider) {
    this.functionsProvider = functionsProvider;
    this.mockServiceProvider = mockServiceProvider;
  }

  @Override
  public CloudFunctionsService get() {
    return provideCloudFunctionsService(functionsProvider.get(), mockServiceProvider.get());
  }

  public static AppModule_ProvideCloudFunctionsServiceFactory create(
      Provider<FirebaseFunctions> functionsProvider,
      Provider<MockCloudFunctionsService> mockServiceProvider) {
    return new AppModule_ProvideCloudFunctionsServiceFactory(functionsProvider, mockServiceProvider);
  }

  public static CloudFunctionsService provideCloudFunctionsService(FirebaseFunctions functions,
      MockCloudFunctionsService mockService) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideCloudFunctionsService(functions, mockService));
  }
}
