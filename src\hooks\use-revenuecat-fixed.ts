import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/contexts/supabase-auth-context';
import { supabase } from '@/lib/supabase';

interface SubscriptionStatus {
  isSubscribed: boolean;
  planType: 'free' | 'monthly' | 'annual';
  expiresAt: Date | null;
  platform: 'web' | 'android' | 'ios' | null;
}

interface PurchasesOffering {
  identifier: string;
  serverDescription: string;
  availablePackages: Array<{
    identifier: string;
    packageType: string;
    product: {
      identifier: string;
      title: string;
      description: string;
      price: number;
      priceString: string;
    };
  }>;
}

interface CustomerInfo {
  activeSubscriptions: string[];
  allPurchasedProductIdentifiers: string[];
  entitlements: {
    active: Record<string, {
      identifier: string;
      isActive: boolean;
      willRenew: boolean;
      periodType: string;
      latestPurchaseDate: string;
      originalPurchaseDate: string;
      expirationDate: string | null;
      store: string;
      productIdentifier: string;
    }>;
  };
}

// Mock del SDK de RevenueCat para evitar errores
const MockPurchases = {
  configure: async (config: { apiKey: string; appUserId: string }) => {
    console.log('🔧 Mock RevenueCat configured for user:', config.appUserId);
  },
  getOfferings: async (): Promise<{ current: PurchasesOffering | null }> => {
    console.log('🔄 Mock getOfferings called');
    return { current: null };
  },
  getCustomerInfo: async (): Promise<CustomerInfo> => {
    console.log('🔄 Mock getCustomerInfo called');
    return {
      activeSubscriptions: [],
      allPurchasedProductIdentifiers: [],
      entitlements: { active: {} }
    };
  },
  purchasePackage: async (packageToPurchase: any) => {
    console.log('🔄 Mock purchasePackage called');
    throw new Error('Purchase not implemented in mock');
  },
  restorePurchases: async (): Promise<CustomerInfo> => {
    console.log('🔄 Mock restorePurchases called');
    return {
      activeSubscriptions: [],
      allPurchasedProductIdentifiers: [],
      entitlements: { active: {} }
    };
  }
};

export const useRevenueCat = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [offerings, setOfferings] = useState<PurchasesOffering | null>(null);
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus>({
    isSubscribed: false,
    planType: 'free',
    expiresAt: null,
    platform: null
  });
  const [isLoading, setIsLoading] = useState(true);
  const { user } = useAuth();

  // Inicializar RevenueCat (usando mock por ahora)
  useEffect(() => {
    const initializeRevenueCat = async () => {
      if (!user || isInitialized) return;

      try {
        // Configurar RevenueCat con el UID del usuario de Supabase
        await MockPurchases.configure({
          apiKey: process.env.NEXT_PUBLIC_REVENUECAT_PUBLIC_KEY_WEB ?? 'mock_key',
          appUserId: user.id
        });

        setIsInitialized(true);
        console.log('✅ RevenueCat initialized for user:', user.id);
      } catch (error) {
        console.error('❌ Error initializing RevenueCat:', error);
      }
    };

    initializeRevenueCat();
  }, [user, isInitialized]);

  // Cargar offerings y estado de suscripción
  useEffect(() => {
    const loadData = async () => {
      if (!isInitialized) return;

      try {
        setIsLoading(true);

        // Cargar offerings
        const offerings = await MockPurchases.getOfferings();
        if (offerings.current) {
          setOfferings(offerings.current);
        }

        // Verificar estado de suscripción
        await checkSubscriptionStatus();
      } catch (error) {
        console.error('❌ Error loading RevenueCat data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [isInitialized]);

  // Helper function to create subscription status from profile
  const createStatusFromProfile = (profile: any): SubscriptionStatus => {
    return {
      isSubscribed: true,
      planType: profile.subscriptionPlanType as 'monthly' | 'annual',
      expiresAt: profile.subscriptionExpiresAt ? new Date(profile.subscriptionExpiresAt) : null,
      platform: profile.subscriptionPlatform as 'web' | 'android' | 'ios'
    };
  };

  // Helper function to get platform from store
  const getPlatformFromStore = (store: string): 'web' | 'android' | 'ios' => {
    if (store === 'STRIPE') return 'web';
    if (store === 'PLAY_STORE') return 'android';
    return 'ios';
  };

  // Helper function to create subscription status from RevenueCat entitlement
  const createStatusFromEntitlement = (entitlement: any): SubscriptionStatus => {
    const isAnnual = entitlement.productIdentifier.includes('annual');
    const platform = getPlatformFromStore(entitlement.store);

    return {
      isSubscribed: true,
      planType: isAnnual ? 'annual' : 'monthly',
      expiresAt: entitlement.expirationDate ? new Date(entitlement.expirationDate) : null,
      platform
    };
  };

  // Helper function to get default free status
  const getDefaultFreeStatus = (): SubscriptionStatus => {
    return {
      isSubscribed: false,
      planType: 'free',
      expiresAt: null,
      platform: null
    };
  };

  // Verificar estado de suscripción
  const checkSubscriptionStatus = useCallback(async (): Promise<SubscriptionStatus> => {
    if (!isInitialized || !user) {
      return getDefaultFreeStatus();
    }

    try {
      // Primero verificar desde Supabase
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscriptionStatus, subscriptionPlatform, subscriptionPlanType, subscriptionExpiresAt')
        .eq('uid', user.id)
        .single();

      if (profile?.subscriptionStatus === 'premium') {
        const status = createStatusFromProfile(profile);
        setSubscriptionStatus(status);
        return status;
      }

      // Si no hay suscripción premium en Supabase, verificar con RevenueCat
      const customerInfo = await MockPurchases.getCustomerInfo();
      const premiumEntitlement = customerInfo.entitlements.active[process.env.NEXT_PUBLIC_REVENUECAT_PREMIUM_ENTITLEMENT ?? 'premium'];

      if (premiumEntitlement?.isActive) {
        const status = createStatusFromEntitlement(premiumEntitlement);
        setSubscriptionStatus(status);
        await syncWithSupabase(status);
        return status;
      } else {
        const status: SubscriptionStatus = {
          isSubscribed: false,
          planType: 'free',
          expiresAt: null,
          platform: null
        };

        setSubscriptionStatus(status);
        await syncWithSupabase(status);
        
        return status;
      }
    } catch (error) {
      console.error('❌ Error checking subscription status:', error);
      return {
        isSubscribed: false,
        planType: 'free',
        expiresAt: null,
        platform: null
      };
    }
  }, [isInitialized, user]);

  // Sincronizar con Supabase
  const syncWithSupabase = async (status: SubscriptionStatus) => {
    if (!user) return;

    try {
      await supabase
        .from('profiles')
        .update({
          subscriptionStatus: status.isSubscribed ? 'premium' : 'free',
          subscriptionPlatform: status.platform,
          subscriptionExpiresAt: status.expiresAt?.toISOString() ?? null,
          subscriptionPlanType: status.planType
        })
        .eq('uid', user.id);

      console.log('✅ Subscription status synced with Supabase:', status);
    } catch (error) {
      console.error('❌ Error syncing with Supabase:', error);
    }
  };

  // Comprar suscripción
  const purchaseSubscription = async (planType: 'monthly' | 'annual'): Promise<boolean> => {
    if (!offerings || !isInitialized) {
      console.error('❌ RevenueCat not initialized or no offerings available');
      return false;
    }

    try {
      const productId = planType === 'monthly' 
        ? process.env.NEXT_PUBLIC_REVENUECAT_MONTHLY_PRODUCT_ID
        : process.env.NEXT_PUBLIC_REVENUECAT_ANNUAL_PRODUCT_ID;

      if (!productId) {
        console.error('❌ Product ID not configured for plan:', planType);
        return false;
      }

      // Buscar el producto en las offerings
      const product = offerings.availablePackages.find(pkg => 
        pkg.product.identifier === productId
      );

      if (!product) {
        console.error('❌ Product not found in offerings:', productId);
        return false;
      }

      console.log('🔄 Purchasing subscription:', planType, productId);
      
      await MockPurchases.purchasePackage(product);
      
      console.log('✅ Purchase successful');
      
      // Verificar y actualizar estado
      await checkSubscriptionStatus();
      
      return true;
    } catch (error) {
      console.error('❌ Error purchasing subscription:', error);
      return false;
    }
  };

  // Restaurar compras
  const restorePurchases = async (): Promise<boolean> => {
    if (!isInitialized) return false;

    try {
      await MockPurchases.restorePurchases();
      console.log('✅ Purchases restored');
      
      await checkSubscriptionStatus();
      return true;
    } catch (error) {
      console.error('❌ Error restoring purchases:', error);
      return false;
    }
  };

  return {
    isInitialized,
    isLoading,
    offerings,
    subscriptionStatus,
    purchaseSubscription,
    restorePurchases,
    checkSubscriptionStatus
  };
};
