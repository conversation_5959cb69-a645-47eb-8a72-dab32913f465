package com.menteencalma.app.presentation.screens.chat;

import com.menteencalma.app.data.service.CloudFunctionsService;
import com.menteencalma.app.domain.repository.AuthRepository;
import com.menteencalma.app.domain.repository.DatabaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ChatViewModel_Factory implements Factory<ChatViewModel> {
  private final Provider<CloudFunctionsService> cloudFunctionsServiceProvider;

  private final Provider<DatabaseRepository> databaseRepositoryProvider;

  private final Provider<AuthRepository> authRepositoryProvider;

  public ChatViewModel_Factory(Provider<CloudFunctionsService> cloudFunctionsServiceProvider,
      Provider<DatabaseRepository> databaseRepositoryProvider,
      Provider<AuthRepository> authRepositoryProvider) {
    this.cloudFunctionsServiceProvider = cloudFunctionsServiceProvider;
    this.databaseRepositoryProvider = databaseRepositoryProvider;
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public ChatViewModel get() {
    return newInstance(cloudFunctionsServiceProvider.get(), databaseRepositoryProvider.get(), authRepositoryProvider.get());
  }

  public static ChatViewModel_Factory create(
      Provider<CloudFunctionsService> cloudFunctionsServiceProvider,
      Provider<DatabaseRepository> databaseRepositoryProvider,
      Provider<AuthRepository> authRepositoryProvider) {
    return new ChatViewModel_Factory(cloudFunctionsServiceProvider, databaseRepositoryProvider, authRepositoryProvider);
  }

  public static ChatViewModel newInstance(CloudFunctionsService cloudFunctionsService,
      DatabaseRepository databaseRepository, AuthRepository authRepository) {
    return new ChatViewModel(cloudFunctionsService, databaseRepository, authRepository);
  }
}
