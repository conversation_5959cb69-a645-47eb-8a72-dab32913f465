package com.menteencalma.app.data.repository;

import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FirebaseDatabaseRepository_Factory implements Factory<FirebaseDatabaseRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  public FirebaseDatabaseRepository_Factory(Provider<FirebaseFirestore> firestoreProvider) {
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public FirebaseDatabaseRepository get() {
    return newInstance(firestoreProvider.get());
  }

  public static FirebaseDatabaseRepository_Factory create(
      Provider<FirebaseFirestore> firestoreProvider) {
    return new FirebaseDatabaseRepository_Factory(firestoreProvider);
  }

  public static FirebaseDatabaseRepository newInstance(FirebaseFirestore firestore) {
    return new FirebaseDatabaseRepository(firestore);
  }
}
