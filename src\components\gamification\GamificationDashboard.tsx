'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useGamification } from '@/hooks/use-gamification';
import { 
  Trophy, 
  Target, 
  Flame, 
  Star, 
  Calendar,
  CheckCircle2,
  Circle,
  Zap,
  Gift,
  ChevronRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';

interface GamificationDashboardProps {
  compact?: boolean;
}

// Helper function for chart bar height style
const getChartBarHeightStyle = (value: number) => ({
  height: `${Math.max(8, (value / 100) * 32)}px`
});

export const GamificationDashboard: React.FC<GamificationDashboardProps> = ({ compact = false }) => {
  const {
    unlockedAchievements,
    dailyTasks,
    dailyTasksCompleted,
    dailyTasksTotal,
    weeklyProgress,
    streak,
    level,
    xp,
    xpForNextLevel,
    completeTask,
    loading
  } = useGamification();

  const progressPercentage = ((xp % (xpForNextLevel / level)) / (xpForNextLevel / level)) * 100;
  const recentAchievements = unlockedAchievements.slice(0, 3);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="animate-pulse">
          <CardHeader className="space-y-2">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-3 bg-muted rounded w-1/2"></div>
          </CardHeader>
          <CardContent>
            <div className="h-2 bg-muted rounded w-full"></div>
          </CardContent>
        </Card>
        <Card className="animate-pulse">
          <CardHeader className="space-y-2">
            <div className="h-4 bg-muted rounded w-3/4"></div>
            <div className="h-3 bg-muted rounded w-1/2"></div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="h-3 bg-muted rounded"></div>
              <div className="h-3 bg-muted rounded"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (compact) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Nivel compacto */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Star className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="text-sm font-medium">Nivel {level}</p>
                  <p className="text-xs text-muted-foreground">{xp} XP</p>
                </div>
              </div>
              <Badge variant="outline" className="text-xs">
                {Math.round(progressPercentage)}%
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Tareas diarias compactas */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Target className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Tareas diarias</p>
                  <p className="text-xs text-muted-foreground">
                    {dailyTasksCompleted}/{dailyTasksTotal} completadas
                  </p>
                </div>
              </div>
              <Badge variant={dailyTasksCompleted === dailyTasksTotal ? "default" : "secondary"}>
                {dailyTasksCompleted === dailyTasksTotal ? "¡Completo!" : "En progreso"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Racha compacta */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <Flame className="h-4 w-4 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm font-medium">Racha</p>
                  <p className="text-xs text-muted-foreground">{streak} días</p>
                </div>
              </div>
              <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                🔥 {streak}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header con progreso de nivel */}
      <Card className="bg-gradient-to-r from-primary/10 via-primary/5 to-transparent border-primary/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5 text-primary" />
                Nivel {level}
              </CardTitle>
              <CardDescription>
                {xp} XP · {xpForNextLevel - xp} XP para el siguiente nivel
              </CardDescription>
            </div>
            <div className="text-right">
              <Badge variant="secondary" className="mb-2">
                {Math.round(progressPercentage)}% progreso
              </Badge>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Zap className="h-4 w-4" />
                {Math.round(progressPercentage)}% al siguiente nivel
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Progress value={progressPercentage} className="h-3" />
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Tareas diarias */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5 text-green-600" />
              Tareas diarias
            </CardTitle>
            <CardDescription>
              {dailyTasksCompleted}/{dailyTasksTotal} completadas hoy
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {dailyTasks.map((task) => (
              <button
                type="button"
                key={task.id}
                className={cn(
                  "flex items-center gap-3 p-3 rounded-lg border transition-all w-full text-left",
                  task.completed
                    ? "bg-green-50 border-green-200 text-green-800"
                    : "bg-muted/50 hover:bg-muted"
                )}
                onClick={() => !task.completed && completeTask(task.id)}
                disabled={task.completed}
              >
                <div className="flex-shrink-0">
                  {task.completed ? (
                    <CheckCircle2 className="h-5 w-5 text-green-600" />
                  ) : (
                    <Circle className="h-5 w-5 text-muted-foreground" />
                  )}
                </div>
                <div className="flex-grow">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{task.icon}</span>
                    <p className={cn(
                      "font-medium",
                      task.completed && "line-through"
                    )}>
                      {task.title}
                    </p>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {task.description}
                  </p>
                </div>
                <Badge variant="outline" className="text-xs">
                  +{task.xpReward} XP
                </Badge>
              </button>
            ))}
          </CardContent>
        </Card>

        {/* Estadísticas y progreso */}
        <div className="space-y-4">
          {/* Racha */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Flame className="h-5 w-5 text-orange-600" />
                Racha de {streak} días
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold text-orange-600">
                  🔥 {streak}
                </div>
                <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                  ¡Sigue así!
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground mt-2">
                Mantén tu consistencia diaria para aumentar tu racha
              </p>
            </CardContent>
          </Card>

          {/* Progreso semanal */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Calendar className="h-5 w-5 text-blue-600" />
                Esta semana
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between items-end gap-1">
                {weeklyProgress.map((day) => (
                  <div key={day.day} className="flex flex-col items-center gap-1">
                    <div
                      className={cn(
                        "w-6 rounded-sm transition-all",
                        day.completed
                          ? "bg-primary"
                          : "bg-muted"
                      )}
                      style={getChartBarHeightStyle(day.value)}
                    />
                    <span className="text-xs text-muted-foreground">
                      {day.day}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Logros recientes */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Trophy className="h-5 w-5 text-yellow-600" />
                  Logros recientes
                </CardTitle>
                <Link href="/achievements">
                  <Button variant="ghost" size="sm" className="text-xs">
                    Ver todos
                    <ChevronRight className="h-3 w-3 ml-1" />
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              {recentAchievements.length > 0 ? (
                <div className="space-y-2">
                  {recentAchievements.map((achievement) => (
                    <div
                      key={achievement.id}
                      className="flex items-center gap-3 p-2 rounded-lg bg-yellow-50 border border-yellow-200"
                    >
                      <div className="text-lg">{achievement.icon}</div>
                      <div className="flex-grow">
                        <p className="font-medium text-sm">{achievement.title}</p>
                        <p className="text-xs text-muted-foreground">
                          +{achievement.xp} XP
                        </p>
                      </div>
                      <Gift className="h-4 w-4 text-yellow-600" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4">
                  <Trophy className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">
                    ¡Completa tareas para desbloquear logros!
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
