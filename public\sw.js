if(!self.define){let e,s={};const i=(i,a)=>(i=new URL(i+".js",a).href,s[i]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=i,e.onload=s,document.head.appendChild(e)}else e=i,importScripts(i),s()})).then((()=>{let e=s[i];if(!e)throw new Error(`Module ${i} didn’t register its module`);return e})));self.define=(a,n)=>{const t=e||("document"in self?document.currentScript.src:"")||location.href;if(s[t])return;let r={};const c=e=>i(e,t),l={module:{uri:t},exports:r,require:c};s[t]=Promise.all(a.map((e=>l[e]||c(e)))).then((e=>(n(...e),r)))}}define(["./workbox-4754cb34"],(function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"61b6a767bae358e5187718af3f231dca"},{url:"/_next/static/PViOvAPWbUeDrlbQsKphl/_buildManifest.js",revision:"aafac71c9b9bf9db12a012ebe2e4db2f"},{url:"/_next/static/PViOvAPWbUeDrlbQsKphl/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/1684-a0c5a2f62e88552b.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/2287-52c6a8e7dcc756e8.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/2448-fae1a22eee9707d7.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/2583-9c75cedbe78d0dfb.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/3214-60ba236a73abed97.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/3234-74420961aa0a50ef.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/3992-a2192516ddad19b3.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/472.2c08b965bd9148e2.js",revision:"2c08b965bd9148e2"},{url:"/_next/static/chunks/4743-55155c60d536bf5c.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/4982-35cf44b61af5a922.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/4bd1b696-415f7c7672dbb9c9.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/5574-e44d106dabd4d229.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/5676-641756fb022b5ad5.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/5756-a29a2675a84987a3.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/5769-f2532f8efe7c697d.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/5884-d672985ca6749e98.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/6055-3b839aacf67312f5.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/622-d1e52e6a0ce66744.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/6321-666e7c8ce52d85c4.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/6329-47b06d98175eaa55.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/64-ea14d1321d2f7d23.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/6407-bb1690a32e940c2e.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/6471-d1c2ed9d63ce3e28.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/7022-ecd8ffa883a0b98a.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/8879-d642098485fa9ed6.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/9341.0bf7523e6a038c05.js",revision:"0bf7523e6a038c05"},{url:"/_next/static/chunks/9613-024ef1229a98f807.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/(app)/achievements/page-deb4f42aa4d570d4.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/(app)/articles/page-edf5a5fb6451bb76.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/(app)/chat/page-13afce5c1f07b300.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/(app)/dashboard/page-fe09a8b548ad6323.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/(app)/disclaimer/page-b79a176a1d57b923.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/(app)/doctors/page-14f01607f58cef76.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/(app)/error-860b87f8d6776edf.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/(app)/focus-garden/page-cec4f61fed9ebbf8.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/(app)/layout-86ae794d2c8b1508.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/(app)/mood-tracker/page-198a040d1301fbc1.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/(app)/profile/page-7670d65737fc883b.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/(app)/recommendations/page-11b839bae53aa392.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/(app)/subscribe/page-5831166a2725ffbd.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/(auth)/create-profile/page-3c3fd86eb5e3fd81.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/_not-found/page-26babafd3b6441f1.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/api/chat/route-2bfd00801e5418da.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/api/generate-analysis/route-e9516987f02becfe.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/api/generate-article/route-d60e23a97d3e6159.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/api/generate-doctor-summary/route-000785480889d723.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/api/recommendations/route-1b72e24897cadc33.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/api/revenuecat-webhook/route-7f944f1ae48109e2.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/api/test/route-84021f45c3ae6f0d.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/auth/callback/page-0906f9983d8f4697.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/dashboard-enhanced-example/page-6e81301f5d8048fd.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/layout-09b45758cc034a4e.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/login/page-0ea248ef2d600621.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/page-00b6d8a3497caf77.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/app/ui-showcase/page-9932cc022e09d9de.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/framework-2c2be674e67eda3d.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/main-3e36ca0291267a3a.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/main-app-ff494e6c1f24767f.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/pages/_app-a61587d9d4172ff4.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/pages/_error-85dc5b0cc18f4481.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-19ed8f1271b36475.js",revision:"PViOvAPWbUeDrlbQsKphl"},{url:"/_next/static/css/7013955605b83be0.css",revision:"7013955605b83be0"},{url:"/_next/static/css/f93703f797ae8542.css",revision:"f93703f797ae8542"},{url:"/check-onboarding-status.js",revision:"a1d0739dd9aa6bc52272ea0a6184aa5b"},{url:"/clear-auth.js",revision:"d7707e41a55b4bb5c21ffaa3fef37e2d"},{url:"/clear-profile-test.js",revision:"189261c0bd54ab20adf59482f8271977"},{url:"/images/avatar-terapeuta-hombre.png",revision:"aab8571e4290e00c9134ef5507c05fc8"},{url:"/images/avatar-terapeuta-mujer.png",revision:"2ba39e6162c6c09d2fd66afc470d4fcd"},{url:"/images/fondo-terreno.svg",revision:"db42908f4c14ddcffc0e6a4348afae69"},{url:"/images/logo-192x192.png",revision:"038d4d670bbb380bd6c41dcf53ae9e79"},{url:"/images/logo-256x256.png",revision:"4d687bc25c985f03a30ce3cb733e66ae"},{url:"/images/logo-384x384.png",revision:"4832041865f4fdc0442b57a9c34db1b8"},{url:"/images/logo-512x512.png",revision:"2a9b784fa8895650bfc869242eb31564"},{url:"/manifest.json",revision:"ddfc56805259895f12e78949ef957fee"},{url:"/test-autosave.js",revision:"f2fed0cc9ed7ba283ca0ded22bd23249"},{url:"/test-form-fix.js",revision:"a390b73d548d2d8df257a9e4acd8bb38"},{url:"/test-incomplete-profile.js",revision:"bdbe7d5390ac0fb4207c46892cdf2500"},{url:"/test-mood-tracker-browser.js",revision:"416b8612548042c1b670f56ff45b1508"},{url:"/verify-dashboard-migration.js",revision:"4db47e9fe4a20910de9816222e4beb4c"},{url:"/verify-database-save.js",revision:"1d440796efbd1623ce685dff3c25e2a7"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:i,state:a})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")}),new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")}),new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>!(self.origin===e.origin)),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")}));
