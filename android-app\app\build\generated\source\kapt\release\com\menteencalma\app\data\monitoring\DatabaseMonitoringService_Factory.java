package com.menteencalma.app.data.monitoring;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseMonitoringService_Factory implements Factory<DatabaseMonitoringService> {
  @Override
  public DatabaseMonitoringService get() {
    return newInstance();
  }

  public static DatabaseMonitoringService_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static DatabaseMonitoringService newInstance() {
    return new DatabaseMonitoringService();
  }

  private static final class InstanceHolder {
    private static final DatabaseMonitoringService_Factory INSTANCE = new DatabaseMonitoringService_Factory();
  }
}
