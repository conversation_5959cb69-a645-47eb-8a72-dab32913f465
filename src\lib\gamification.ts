// Funciones de gamificación y límites para la web
export const FREE_USER_LIMITS = {
  dailyChatMessages: 5,
  dailyRecommendations: 3,
  monthlyArticles: 3, // Corregido: 3 artículos al mes para usuarios free
  maxSavedArticles: 5,
};

export const PREMIUM_USER_LIMITS = {
  dailyChatMessages: -1, // Ilimitado
  dailyRecommendations: -1, // Ilimitado
  monthlyArticles: -1, // Ilimitado
  maxSavedArticles: -1, // Ilimitado
};

// Funciones de gamificación
export const getLevel = (xp: number): number => {
  return Math.floor(Math.sqrt(xp / 100)) + 1;
};

export const getXPForNextLevel = (level: number): number => {
  return Math.pow(level, 2) * 100;
};

export const getXPProgress = (currentXP: number) => {
  const level = getLevel(currentXP);
  const xpForNext = getXPForNextLevel(level);
  const progress = (currentXP / xpForNext) * 100;
  
  return { level, progress, xpForNext };
};

// Lista de logros/achievements
export const ACHIEVEMENTS_LIST = [
  { id: 'FIRST_STEPS', title: 'Primeros Pasos', description: 'Crea tu perfil y comienza tu viaje.', icon: '👋', reward: 10, xp: 50, category: 'social' },
  { id: 'CHAT_STARTER', title: 'Conversador', description: 'Envía tu primer mensaje a la IA.', icon: '💬', reward: 5, xp: 25, category: 'engagement' },
  { id: 'FOCUS_ROOKIE', title: 'Principiante del Focus', description: 'Completa tu primera sesión de focus.', icon: '🌱', reward: 15, xp: 75, category: 'productivity' },
  { id: 'STREAK_7_DAYS', title: 'Una Semana Fuerte', description: 'Mantén una racha de 7 días consecutivos.', icon: '🔥', reward: 50, xp: 200, category: 'wellness' },
  { id: 'FOCUS_MASTER', title: 'Maestro del Focus', description: 'Completa 50 sesiones de focus.', icon: '🧘‍♀️', reward: 100, xp: 500, category: 'productivity' },
  { id: 'OPEN_HEART', title: 'Corazón Abierto', description: 'Completa tu primer registro de estado de ánimo.', icon: '❤️', reward: 20, xp: 100, category: 'wellness' },
  { id: 'ARTICLE_READER', title: 'Lector Ávido', description: 'Lee tu primer artículo generado.', icon: '📖', reward: 10, xp: 50, category: 'engagement' },
  { id: 'CONSTANT_CHATTER', title: 'Chateador Constante', description: 'Envía más de 100 mensajes a la IA.', icon: '⭐', reward: 150, xp: 300, category: 'engagement' },
  { id: 'WELLNESS_MASTER', title: 'Maestro del Bienestar', description: 'Desbloquea todos los demás logros.', icon: '🏆', reward: 500, xp: 1000, category: 'wellness' },
];

// Funciones de validación de límites para usuarios free
export const checkUserLimits = (userProfile: any, subscriptionActive: boolean) => {
  if (subscriptionActive) {
    return {
      canSendMessage: true,
      canGenerateRecommendation: true,
      canGenerateArticle: true,
      canSaveArticle: true,
      remainingMessages: -1, // Ilimitado
      remainingRecommendations: -1, // Ilimitado
      remainingArticles: -1, // Ilimitado
      remainingSavedArticles: -1, // Ilimitado
    };
  }

  const today = new Date().toISOString().split('T')[0];
  const currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM
  // Contar uso diario/mensual basado en los campos del userProfile
  const todayMessages = userProfile?.daily_chat_count ?? 0;
  const isToday = userProfile?.last_chat_date === today;
  const actualTodayMessages = isToday ? todayMessages : 0;

  const monthlyArticles = userProfile?.usage?.articlesViewed?.count ?? 0;
  const isCurrentMonth = userProfile?.usage?.articlesViewed?.lastViewedMonth === currentMonth;
  const actualMonthlyArticles = isCurrentMonth ? monthlyArticles : 0;

  // Simular recomendaciones (no hay campo específico, usar chat como proxy)
  const todayRecommendations = Math.floor(actualTodayMessages / 2); // Aproximación

  const savedArticles = userProfile?.savedArticles?.length ?? 0;

  return {
    canSendMessage: actualTodayMessages < FREE_USER_LIMITS.dailyChatMessages,
    canGenerateRecommendation: todayRecommendations < FREE_USER_LIMITS.dailyRecommendations,
    canGenerateArticle: actualMonthlyArticles < FREE_USER_LIMITS.monthlyArticles,
    canSaveArticle: savedArticles < FREE_USER_LIMITS.maxSavedArticles,
    remainingMessages: Math.max(0, FREE_USER_LIMITS.dailyChatMessages - actualTodayMessages),
    remainingRecommendations: Math.max(0, FREE_USER_LIMITS.dailyRecommendations - todayRecommendations),
    remainingArticles: Math.max(0, FREE_USER_LIMITS.monthlyArticles - actualMonthlyArticles),
    remainingSavedArticles: Math.max(0, FREE_USER_LIMITS.maxSavedArticles - savedArticles),
  };
};

export const getLimitMessage = (limitType: string, subscriptionActive: boolean): string | null => {
  if (subscriptionActive) return null;
  
  const messages: Record<string, string> = {
    chat: `Has alcanzado el límite de ${FREE_USER_LIMITS.dailyChatMessages} mensajes diarios. Actualiza a Premium para chat ilimitado.`,
    recommendations: `Has alcanzado el límite de ${FREE_USER_LIMITS.dailyRecommendations} recomendaciones diarias. Actualiza a Premium.`,
    articles: `Has alcanzado el límite de ${FREE_USER_LIMITS.monthlyArticles} artículos mensuales. Actualiza a Premium.`,
    savedArticles: `Has alcanzado el límite de ${FREE_USER_LIMITS.maxSavedArticles} artículos guardados. Actualiza a Premium.`,
  };
  
  return messages[limitType] || 'Límite alcanzado. Actualiza a Premium para acceso ilimitado.';
};

// Funciones auxiliares para verificar logros
export const checkUnlockedAchievements = (userProfile: any, userStats: any): string[] => {
  const unlocked: string[] = [];
  
  ACHIEVEMENTS_LIST.forEach(achievement => {
    if (userProfile.unlockedAchievements?.includes(achievement.id)) {
      return; // Ya desbloqueado
    }
    
    let shouldUnlock = false;
    
    switch (achievement.id) {
      case 'FIRST_STEPS':
        shouldUnlock = !!userProfile.displayName || !!userProfile.full_name;
        break;      case 'CHAT_STARTER':
        shouldUnlock = (userProfile.daily_chat_count ?? 0) > 0;
        break;
      case 'FOCUS_ROOKIE':
        shouldUnlock = (userProfile.focusSessionsHistory?.length ?? 0) > 0;
        break;
      case 'STREAK_7_DAYS':
        shouldUnlock = userStats.currentStreak >= 7;
        break;
      case 'FOCUS_MASTER':
        shouldUnlock = (userProfile.focusSessionsHistory?.length ?? 0) >= 50;
        break;
      case 'OPEN_HEART':
        shouldUnlock = userStats.moodEntries > 0;
        break;
      case 'ARTICLE_READER':
        shouldUnlock = (userProfile.usage?.articlesViewed?.count ?? 0) > 0;
        break;
      case 'CONSTANT_CHATTER':
        shouldUnlock = userStats.totalChatMessages >= 100;
        break;
      case 'WELLNESS_MASTER': {
        const otherAchievements = ACHIEVEMENTS_LIST.filter(a => a.id !== 'WELLNESS_MASTER');
        const unlockedCount = otherAchievements.filter(a => 
          userProfile.unlockedAchievements?.includes(a.id)
        ).length;
        shouldUnlock = unlockedCount === otherAchievements.length;
        break;
      }
    }
    
    if (shouldUnlock) {
      unlocked.push(achievement.id);
    }
  });
  
  return unlocked;
};
