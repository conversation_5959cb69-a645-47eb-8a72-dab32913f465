
import { NextResponse } from 'next/server';
import { aiChatbotSupport, type AIChatbotSupportInput } from '@/ai/flows/ai-chatbot-support';
import { supabaseServer } from '@/lib/supabase-server';

// Constantes
const DAILY_CHAT_LIMIT = 5;

// Tipos
interface UserProfileData {
  isPremium: boolean;
  dailyChatCount: number;
}

// Función para verificar conectividad con Supabase
async function testSupabaseConnection(): Promise<boolean> {
  try {
    const { data: testConnection, error: testError } = await supabaseServer
      .from('profiles')
      .select('id')
      .limit(1);

    console.log('🧪 Test connection result:', { hasData: !!testConnection, error: testError });
    return !testError;
  } catch (error) {
    console.error('❌ Connection test failed:', error);
    return false;
  }
}

// Función para crear un perfil por defecto
async function createDefaultProfile(uid: string): Promise<UserProfileData> {
  const today = new Date().toISOString().split('T')[0];

  try {
    const { data: newProfile, error: createError } = await supabaseServer
      .from('profiles')
      .insert({
        id: uid,
        daily_chat_count: 0,
        subscription_status: 'free',
        last_chat_date: today
      })
      .select('daily_chat_count, subscription_status, last_chat_date')
      .single();

    if (createError) {
      console.error('❌ Error creating profile:', createError);
      return { isPremium: false, dailyChatCount: 0 };
    }

    return {
      isPremium: newProfile.subscription_status === 'premium',
      dailyChatCount: newProfile.daily_chat_count ?? 0
    };
  } catch (insertError) {
    console.error('❌ Failed to create profile:', insertError);
    return { isPremium: false, dailyChatCount: 0 };
  }
}

// Función para obtener análisis psicológico del usuario
async function getPsychologicalAnalysis(uid: string) {
  try {
    const { data: analysisData } = await supabaseServer
      .from('user_psychological_analysis')
      .select('*')
      .eq('user_id', uid)
      .order('updated_at', { ascending: false })
      .limit(1);

    return analysisData && analysisData.length > 0 ? analysisData[0] : null;
  } catch (error: unknown) {
    console.log('No psychological analysis found for user:', uid, error);
    return null;
  }
}

// Función para obtener entradas recientes de mood
async function getRecentMoodEntries(uid: string) {
  try {
    const { data: moodData } = await supabaseServer
      .from('mood_entries')
      .select('mood, value, description, date')
      .eq('user_id', uid)
      .order('created_at', { ascending: false })
      .limit(10);

    return moodData ?? [];
  } catch (error: unknown) {
    console.log('No mood entries found for user:', uid, error);
    return [];
  }
}

// Función para obtener perfil completo del usuario
async function getEnhancedProfile(uid: string, profile: any) {
  if (profile) {
    return profile;
  }

  try {
    const { data: profileData } = await supabaseServer
      .from('profiles')
      .select('*')
      .eq('id', uid)
      .single();

    if (profileData) {
      return {
        fullName: profileData.full_name,
        age: profileData.age,
        gender: profileData.gender,
        mentalHealthHistory: profileData.previous_therapy,
        currentChallenges: Array.isArray(profileData.main_concerns)
          ? profileData.main_concerns
          : [],
        therapistGender: profileData.preferred_therapist === 'alejandro' ? 'male' : 'female',
        preferences: profileData.communication_style
      };
    }
  } catch (error: unknown) {
    console.log('Error fetching enhanced profile:', error);
  }

  return null;
}

// Función para actualizar el contador de chat
async function updateChatCount(uid: string, isPremium: boolean, dailyChatCount: number): Promise<number> {
  if (isPremium) {
    return dailyChatCount;
  }

  const newDailyChatCount = dailyChatCount + 1;
  const today = new Date().toISOString().split('T')[0];

  const { error: updateError } = await supabaseServer
    .from('profiles')
    .update({
      daily_chat_count: newDailyChatCount,
      last_chat_date: today
    })
    .eq('id', uid);

  if (updateError) {
    console.error('❌ Error updating chat count:', updateError);
    return dailyChatCount;
  }

  console.log('✅ Chat count updated successfully:', {
    oldCount: dailyChatCount,
    newCount: newDailyChatCount
  });

  return newDailyChatCount;
}

// Función para obtener y procesar datos del perfil de usuario
async function getUserProfileData(uid: string): Promise<UserProfileData> {
  const { data: userProfile, error: profileError } = await supabaseServer
    .from('profiles')
    .select('daily_chat_count, subscription_status, last_chat_date')
    .eq('id', uid)
    .single();

  console.log('📊 Profile query result:', { userProfile, profileError });

  if (profileError) {
    console.error('❌ Profile error details:', {
      message: profileError.message,
      details: profileError.details,
      hint: profileError.hint,
      code: profileError.code
    });

    console.log('🔧 Creating default profile for user');
    return await createDefaultProfile(uid);
  }

  if (!userProfile) {
    if (process.env.NODE_ENV !== 'development') {
      throw new Error('User profile not found');
    }
    return { isPremium: false, dailyChatCount: 0 };
  }

  const today = new Date().toISOString().split('T')[0];
  const isPremium = userProfile.subscription_status === 'premium';

  // Verificar si necesitamos resetear el contador diario
  const lastChatDate = userProfile.last_chat_date;
  const lastChatDay = lastChatDate ? lastChatDate.split('T')[0] : null;
  const isNewDay = !lastChatDay || lastChatDay !== today;

  console.log('📅 Date check:', { today, lastChatDay, isNewDay });

  let dailyChatCount: number;

  if (isNewDay) {
    console.log('🔄 New day detected, resetting daily chat count');
    dailyChatCount = 0;

    // Actualizar la base de datos con el reset
    await supabaseServer
      .from('profiles')
      .update({
        daily_chat_count: 0,
        last_chat_date: today
      })
      .eq('id', uid);
  } else {
    dailyChatCount = userProfile.daily_chat_count ?? 0;
  }

  console.log('✅ Using actual profile data:', { isPremium, dailyChatCount, isNewDay });
  return { isPremium, dailyChatCount };
}

export async function POST(req: Request) {
  try {
    console.log('🔥 Chat API called');
    
    // Verificar variables de entorno
    console.log('🔍 Environment check:', {
      hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL
    });
    
    const body = await req.json();
    const { uid, message, profile, conversationHistory } = body as AIChatbotSupportInput;

    console.log('📥 Request body:', { uid: uid?.substring(0, 8) + '...', message: message?.substring(0, 50) + '...', hasProfile: !!profile });

    if (!uid || !message) {
      console.log('❌ Missing required fields:', { uid: !!uid, message: !!message });
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Verificar conectividad con Supabase
    console.log('🔍 Looking for user profile with UID:', uid);
    await testSupabaseConnection();

    // Obtener datos del perfil de usuario
    const { isPremium, dailyChatCount } = await getUserProfileData(uid);

    // Verificar límites solo para usuarios no premium
    if (!isPremium && dailyChatCount >= DAILY_CHAT_LIMIT) {
      return NextResponse.json({ 
        error: 'MESSAGE_LIMIT_REACHED',
        message: 'Has alcanzado el límite diario de mensajes. Actualiza a Premium para acceso ilimitado.' 
      }, { status: 429 });
    }

    // Obtener análisis psicológico del usuario
    const psychologicalAnalysis = await getPsychologicalAnalysis(uid);

    // Obtener entradas recientes de mood
    const recentMoodEntries = await getRecentMoodEntries(uid);

    // Obtener perfil completo del usuario
    const enhancedProfile = await getEnhancedProfile(uid, profile);

    const aiResponse = await aiChatbotSupport({
      uid,
      message,
      profile: enhancedProfile ?? {}, // Enviar objeto vacío en lugar de null
      conversationHistory,
      psychologicalAnalysis: psychologicalAnalysis ?? {}, // Enviar objeto vacío en lugar de null
      recentMoodEntries,
    });

    // Incrementar contador de chat después de respuesta exitosa
    const newDailyChatCount = await updateChatCount(uid, isPremium, dailyChatCount);

    return NextResponse.json({
      ...aiResponse,
      // Incluir información actualizada de límites
      usage: {
        dailyChatCount: isPremium ? 0 : newDailyChatCount,
        dailyChatLimit: isPremium ? Infinity : DAILY_CHAT_LIMIT,
        isPremium
      }
    });
  } catch (error: unknown) {
    console.error('Error in chat API:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : 'UnknownError'
    });
    
    // Manejo específico del error de límites
    if (error instanceof Error && error.message === 'MESSAGE_LIMIT_REACHED') {
      return NextResponse.json({ 
        error: 'MESSAGE_LIMIT_REACHED',
        message: 'Has alcanzado el límite diario de mensajes. Actualiza a Premium para acceso ilimitado.' 
      }, { status: 429 });
    }
    
    const getErrorDetails = () => {
      if (process.env.NODE_ENV !== 'development') return undefined;
      return error instanceof Error ? error.message : 'Unknown error';
    };
    
    return NextResponse.json({ 
      error: 'Internal server error',
      details: getErrorDetails()
    }, { status: 500 });
  }
}
