package com.menteencalma.app.data.repository;

import com.menteencalma.app.data.monitoring.DatabaseMonitoringService;
import com.menteencalma.app.domain.repository.DatabaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MonitoredDatabaseRepository_Factory implements Factory<MonitoredDatabaseRepository> {
  private final Provider<DatabaseRepository> actualRepositoryProvider;

  private final Provider<DatabaseMonitoringService> monitoringServiceProvider;

  public MonitoredDatabaseRepository_Factory(Provider<DatabaseRepository> actualRepositoryProvider,
      Provider<DatabaseMonitoringService> monitoringServiceProvider) {
    this.actualRepositoryProvider = actualRepositoryProvider;
    this.monitoringServiceProvider = monitoringServiceProvider;
  }

  @Override
  public MonitoredDatabaseRepository get() {
    return newInstance(actualRepositoryProvider.get(), monitoringServiceProvider.get());
  }

  public static MonitoredDatabaseRepository_Factory create(
      Provider<DatabaseRepository> actualRepositoryProvider,
      Provider<DatabaseMonitoringService> monitoringServiceProvider) {
    return new MonitoredDatabaseRepository_Factory(actualRepositoryProvider, monitoringServiceProvider);
  }

  public static MonitoredDatabaseRepository newInstance(DatabaseRepository actualRepository,
      DatabaseMonitoringService monitoringService) {
    return new MonitoredDatabaseRepository(actualRepository, monitoringService);
  }
}
