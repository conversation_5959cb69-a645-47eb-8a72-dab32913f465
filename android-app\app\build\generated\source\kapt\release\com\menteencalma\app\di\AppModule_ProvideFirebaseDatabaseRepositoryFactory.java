package com.menteencalma.app.di;

import com.google.firebase.firestore.FirebaseFirestore;
import com.menteencalma.app.domain.repository.DatabaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("com.menteencalma.app.di.FirebaseDatabase")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideFirebaseDatabaseRepositoryFactory implements Factory<DatabaseRepository> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  public AppModule_ProvideFirebaseDatabaseRepositoryFactory(
      Provider<FirebaseFirestore> firestoreProvider) {
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public DatabaseRepository get() {
    return provideFirebaseDatabaseRepository(firestoreProvider.get());
  }

  public static AppModule_ProvideFirebaseDatabaseRepositoryFactory create(
      Provider<FirebaseFirestore> firestoreProvider) {
    return new AppModule_ProvideFirebaseDatabaseRepositoryFactory(firestoreProvider);
  }

  public static DatabaseRepository provideFirebaseDatabaseRepository(FirebaseFirestore firestore) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideFirebaseDatabaseRepository(firestore));
  }
}
