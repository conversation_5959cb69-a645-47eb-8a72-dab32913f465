package com.menteencalma.app.presentation.viewmodels;

import com.google.firebase.functions.FirebaseFunctions;
import com.menteencalma.app.domain.repository.AuthRepository;
import com.menteencalma.app.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CreateProfileViewModel_Factory implements Factory<CreateProfileViewModel> {
  private final Provider<AuthRepository> authRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<FirebaseFunctions> functionsProvider;

  public CreateProfileViewModel_Factory(Provider<AuthRepository> authRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider,
      Provider<FirebaseFunctions> functionsProvider) {
    this.authRepositoryProvider = authRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
    this.functionsProvider = functionsProvider;
  }

  @Override
  public CreateProfileViewModel get() {
    return newInstance(authRepositoryProvider.get(), userRepositoryProvider.get(), functionsProvider.get());
  }

  public static CreateProfileViewModel_Factory create(
      Provider<AuthRepository> authRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider,
      Provider<FirebaseFunctions> functionsProvider) {
    return new CreateProfileViewModel_Factory(authRepositoryProvider, userRepositoryProvider, functionsProvider);
  }

  public static CreateProfileViewModel newInstance(AuthRepository authRepository,
      UserRepository userRepository, FirebaseFunctions functions) {
    return new CreateProfileViewModel(authRepository, userRepository, functions);
  }
}
