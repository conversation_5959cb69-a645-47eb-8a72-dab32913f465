import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Modal,
  TextInput
} from 'react-native';
import { useAuth } from '../contexts/SupabaseAuthContext';
import { useSubscription } from '../contexts/SupabaseSubscriptionContext';
import { getLevel, getXPForNextLevel } from '../types/gamification';

// Función auxiliar para obtener el avatar emoji
const getAvatarEmoji = (gender) => {
  if (gender === 'female') return '👩';
  if (gender === 'male') return '👨';
  return '🧘‍♀️';
};

// Función auxiliar para obtener el nombre del género
const getGenderName = (gender) => {
  if (gender === 'male') return 'Masculino';
  if (gender === 'female') return 'Femenino';
  return 'Otro';
};

// Componente separado para evitar re-renderizado
const StatCard = ({ title, value, subtitle, icon }) => (
  <View style={styles.statCard}>
    <Text style={styles.statIcon}>{icon}</Text>
    <Text style={styles.statValue}>{value}</Text>
    <Text style={styles.statTitle}>{title}</Text>
    {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
  </View>
);

StatCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  subtitle: PropTypes.string,
  icon: PropTypes.string.isRequired,
};

const ProfileScreen = ({ navigation }) => {
  const { user, userProfile, saveUserProfile, logout } = useAuth();
  const { isSubscriptionActive, getSubscriptionPlan } = useSubscription();
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [editedProfile, setEditedProfile] = useState({
    displayName: userProfile?.displayName || '',
    age: userProfile?.age || '',
    gender: userProfile?.gender || '',
    therapistGender: userProfile?.therapistGender || 'female',
  });

  const currentLevel = getLevel(userProfile?.xp || 0);
  const xpForNext = getXPForNextLevel(currentLevel);
  const currentXP = userProfile?.xp || 0;
  const progressPercentage = ((currentXP % xpForNext) / xpForNext) * 100;

  const handleSaveProfile = async () => {
    try {
      const updatedProfile = {
        ...userProfile,
        ...editedProfile,
        age: parseInt(editedProfile.age) || userProfile?.age || null,
      };
      
      await saveUserProfile(updatedProfile);
      setIsEditingProfile(false);
      Alert.alert('Éxito', 'Perfil actualizado correctamente');
    } catch (error) {
      console.error('Error saving profile:', error);
      Alert.alert('Error', 'No se pudo actualizar el perfil');
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Cerrar Sesión',
      '¿Estás seguro de que quieres cerrar sesión?',
      [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Sí, cerrar sesión', onPress: logout, style: 'destructive' }
      ]
    );
  };
  const subscriptionActive = isSubscriptionActive();
  const subscriptionPlan = getSubscriptionPlan();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header del perfil */}
        <View style={styles.header}>          <View style={styles.avatarContainer}>
            <Text style={styles.avatarEmoji}>
              {getAvatarEmoji(userProfile?.gender)}
            </Text>
          </View>
          <Text style={styles.profileName}>
            {userProfile?.displayName || user?.email?.split('@')[0] || 'Usuario'}
          </Text>
          <Text style={styles.profileEmail}>{user?.email}</Text>
          
          {/* Nivel y progreso */}
          <View style={styles.levelContainer}>
            <Text style={styles.levelText}>Nivel {currentLevel}</Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: `${progressPercentage}%` }]} />
            </View>
            <Text style={styles.progressText}>
              {currentXP % xpForNext} / {xpForNext} XP
            </Text>
          </View>

          <TouchableOpacity
            style={styles.editButton}
            onPress={() => {
              setEditedProfile({
                displayName: userProfile?.displayName || '',
                age: userProfile?.age?.toString() || '',
                gender: userProfile?.gender || '',
                therapistGender: userProfile?.therapistGender || 'female',
              });
              setIsEditingProfile(true);
            }}
          >
            <Text style={styles.editButtonText}>Editar Perfil</Text>
          </TouchableOpacity>
        </View>

        {/* Estadísticas */}
        <View style={styles.statsSection}>
          <Text style={styles.sectionTitle}>Estadísticas</Text>
          <View style={styles.statsGrid}>
            <StatCard
              title="XP Total"
              value={userProfile?.xp || 0}
              icon="⚡"
            />
            <StatCard
              title="CalmaCoins"
              value={userProfile?.calmaCoins || 0}
              icon="🪙"
            />
            <StatCard
              title="Sesiones Focus"
              value={userProfile?.focusSessionsHistory?.length || 0}
              icon="🌱"
            />
            <StatCard
              title="Logros"
              value={userProfile?.unlockedAchievements?.length || 0}
              icon="🏆"
            />
          </View>
        </View>

        {/* Estado de suscripción */}
        <View style={styles.subscriptionSection}>
          <Text style={styles.sectionTitle}>Suscripción</Text>
          <View style={styles.subscriptionCard}>
            <View style={styles.subscriptionHeader}>
              <View style={[
                styles.subscriptionIndicator,
                { backgroundColor: subscriptionActive ? '#4CAF50' : '#FF9800' }
              ]} />
              <Text style={styles.subscriptionTitle}>
                {subscriptionActive ? `Plan ${subscriptionPlan}` : 'Plan Gratuito'}
              </Text>
            </View>
            <Text style={styles.subscriptionDescription}>
              {subscriptionActive 
                ? 'Tienes acceso completo a todas las funciones premium.'
                : 'Actualiza a premium para acceso ilimitado.'}
            </Text>
            {!subscriptionActive && (
              <TouchableOpacity
                style={styles.upgradeButton}
                onPress={() => navigation.navigate('Subscription')}
              >
                <Text style={styles.upgradeButtonText}>Actualizar a Premium</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Configuraciones */}
        <View style={styles.settingsSection}>
          <Text style={styles.sectionTitle}>Configuraciones</Text>
          
          <TouchableOpacity style={styles.settingItem}>
            <Text style={styles.settingIcon}>🔔</Text>
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>Notificaciones</Text>
              <Text style={styles.settingSubtitle}>Gestionar recordatorios</Text>
            </View>
            <Text style={styles.settingArrow}>›</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.settingItem}>
            <Text style={styles.settingIcon}>🌙</Text>
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>Modo Oscuro</Text>
              <Text style={styles.settingSubtitle}>Tema de la aplicación</Text>
            </View>
            <Text style={styles.settingArrow}>›</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.settingItem}>
            <Text style={styles.settingIcon}>📱</Text>
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>Datos y Privacidad</Text>
              <Text style={styles.settingSubtitle}>Gestionar tu información</Text>
            </View>
            <Text style={styles.settingArrow}>›</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.settingItem}>
            <Text style={styles.settingIcon}>ℹ️</Text>
            <View style={styles.settingContent}>
              <Text style={styles.settingTitle}>Ayuda y Soporte</Text>
              <Text style={styles.settingSubtitle}>Obtener asistencia</Text>
            </View>
            <Text style={styles.settingArrow}>›</Text>
          </TouchableOpacity>
        </View>

        {/* Botón de cerrar sesión */}
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutButtonText}>Cerrar Sesión</Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Modal de edición de perfil */}
      <Modal
        visible={isEditingProfile}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsEditingProfile(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Editar Perfil</Text>
            
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Nombre</Text>
              <TextInput
                style={styles.textInput}
                value={editedProfile.displayName}
                onChangeText={(text) => setEditedProfile({...editedProfile, displayName: text})}
                placeholder="Tu nombre"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Edad</Text>
              <TextInput
                style={styles.textInput}
                value={editedProfile.age}
                onChangeText={(text) => setEditedProfile({...editedProfile, age: text})}
                placeholder="Tu edad"
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Género</Text>
              <View style={styles.genderButtons}>
                {['male', 'female', 'otro'].map((gender) => (
                  <TouchableOpacity
                    key={gender}
                    style={[
                      styles.genderButton,
                      editedProfile.gender === gender && styles.genderButtonActive
                    ]}
                    onPress={() => setEditedProfile({...editedProfile, gender})}
                  >
                    <Text style={[
                      styles.genderButtonText,
                      editedProfile.gender === gender && styles.genderButtonTextActive                    ]}>
                      {getGenderName(gender)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Terapeuta Preferido</Text>
              <View style={styles.genderButtons}>
                {['female', 'male'].map((therapistGender) => (
                  <TouchableOpacity
                    key={therapistGender}
                    style={[
                      styles.genderButton,
                      editedProfile.therapistGender === therapistGender && styles.genderButtonActive
                    ]}
                    onPress={() => setEditedProfile({...editedProfile, therapistGender})}
                  >
                    <Text style={[
                      styles.genderButtonText,
                      editedProfile.therapistGender === therapistGender && styles.genderButtonTextActive
                    ]}>
                      {therapistGender === 'female' ? '👩‍⚕️ Femenino' : '👨‍⚕️ Masculino'}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.modalButtonSecondary}
                onPress={() => setIsEditingProfile(false)}
              >
                <Text style={styles.modalButtonSecondaryText}>Cancelar</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.modalButtonPrimary}
                onPress={handleSaveProfile}
              >
                <Text style={styles.modalButtonPrimaryText}>Guardar</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

ProfileScreen.propTypes = {
  navigation: PropTypes.object.isRequired,
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: '#fff',
    alignItems: 'center',
    padding: 24,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#e3f2fd',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarEmoji: {
    fontSize: 40,
  },
  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 8,
  },
  profileEmail: {
    fontSize: 16,
    color: '#64748b',
    marginBottom: 20,
  },
  levelContainer: {
    width: '100%',
    alignItems: 'center',
    marginBottom: 20,
  },
  levelText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#4A90E2',
    marginBottom: 8,
  },
  progressBar: {
    width: '80%',
    height: 8,
    backgroundColor: '#e2e8f0',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4A90E2',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: '#64748b',
  },
  editButton: {
    backgroundColor: '#4A90E2',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  editButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  statsSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    width: '48%',
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 4,
  },
  statTitle: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
  },
  statSubtitle: {
    fontSize: 12,
    color: '#94a3b8',
    marginTop: 2,
  },
  subscriptionSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  subscriptionCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  subscriptionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  subscriptionIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  subscriptionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
  },
  subscriptionDescription: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 16,
    lineHeight: 20,
  },
  upgradeButton: {
    backgroundColor: '#4A90E2',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  upgradeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  settingsSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  settingItem: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  settingIcon: {
    fontSize: 20,
    marginRight: 16,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1e293b',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#64748b',
  },
  settingArrow: {
    fontSize: 20,
    color: '#cbd5e1',
  },
  logoutButton: {
    backgroundColor: '#dc2626',
    marginHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 40,
  },
  logoutButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 24,
    margin: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 24,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1e293b',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1e293b',
  },
  genderButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  genderButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    backgroundColor: '#f8fafc',
  },
  genderButtonActive: {
    backgroundColor: '#4A90E2',
    borderColor: '#4A90E2',
  },
  genderButtonText: {
    fontSize: 14,
    color: '#64748b',
  },
  genderButtonTextActive: {
    color: '#fff',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  modalButtonSecondary: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    alignItems: 'center',
    marginRight: 8,
  },
  modalButtonSecondaryText: {
    fontSize: 16,
    color: '#64748b',
  },
  modalButtonPrimary: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#4A90E2',
    alignItems: 'center',
    marginLeft: 8,
  },
  modalButtonPrimaryText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});

export default ProfileScreen;
