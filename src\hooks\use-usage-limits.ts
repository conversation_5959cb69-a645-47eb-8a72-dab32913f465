import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/supabase-auth-context';
import { supabase } from '@/lib/supabase';

interface UsageLimits {
  articlesUsed: number;
  articlesLimit: number;
  articlesRemaining: number;
  chatMessagesUsed: number;
  chatMessagesLimit: number;
  chatMessagesRemaining: number;
  isLoadingUsage: boolean;
  refreshUsage: () => Promise<void>;
}

export function useUsageLimits(): UsageLimits {
  const { user, userProfile } = useAuth();
  const [articlesUsed, setArticlesUsed] = useState(0);
  const [chatMessagesUsed, setChatMessagesUsed] = useState(0);
  const [isLoadingUsage, setIsLoadingUsage] = useState(true);
  // Límites basados en el tipo de suscripción
  const isPremium = userProfile?.subscriptionStatus === 'premium';
  const articlesLimit = isPremium ? Infinity : 5; // Corregido: era 3, debe ser 5
  const chatMessagesLimit = isPremium ? Infinity : 5;

  const refreshUsage = async () => {
    if (!user) {
      setIsLoadingUsage(false);
      return;
    }

    setIsLoadingUsage(true);

    try {
      const now = new Date();
      const currentMonth = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`;

      // Obtener uso de artículos del mes actual
      const { data: articlesData, error: articlesError } = await supabase
        .from('user_usage')
        .select('count')
        .eq('user_id', user.id)
        .eq('usage_type', 'articles')
        .eq('month', currentMonth)
        .single();

      if (articlesError && articlesError.code !== 'PGRST116') {
        console.error('Error fetching articles usage:', articlesError);
      } else {
        setArticlesUsed(articlesData?.count ?? 0);
      }

      // Obtener uso de chat del día actual directamente de la base de datos
      // En lugar de confiar solo en userProfile que puede estar desactualizado
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('daily_chat_count, subscription_status')
        .eq('id', user.id)
        .single();

      if (profileError) {
        console.error('Error fetching profile for usage:', profileError);
        // Usar datos del userProfile como fallback
        setChatMessagesUsed(userProfile?.dailyChatCount ?? 0);
      } else {
        setChatMessagesUsed(profileData?.daily_chat_count ?? 0);
      }

    } catch (error) {
      console.error('Error fetching usage data:', error);
    } finally {
      setIsLoadingUsage(false);
    }
  };

  useEffect(() => {
    refreshUsage();
  }, [user, userProfile]);

  // Escuchar actualizaciones de uso del chat
  useEffect(() => {
    const handleUsageUpdate = (event: any) => {
      console.log('🔄 Usage updated, refreshing limits...', event.detail);
      refreshUsage();
    };

    window.addEventListener('chatUsageUpdated', handleUsageUpdate);

    return () => {
      window.removeEventListener('chatUsageUpdated', handleUsageUpdate);
    };
  }, [refreshUsage]);

  return {
    articlesUsed,
    articlesLimit: isPremium ? Infinity : articlesLimit,
    articlesRemaining: isPremium ? Infinity : Math.max(0, articlesLimit - articlesUsed),
    chatMessagesUsed,
    chatMessagesLimit: isPremium ? Infinity : chatMessagesLimit,
    chatMessagesRemaining: isPremium ? Infinity : Math.max(0, chatMessagesLimit - chatMessagesUsed),
    isLoadingUsage,
    refreshUsage,
  };
}
