/**
 * Dashboard mejorado con UX/UI superior
 * Implementa personalización, microinteracciones y mejor jerarquía visual
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/supabase-auth-context';
import { UsageLimitsCard } from './UsageLimitsCard';
import { GamificationDashboard } from '@/components/gamification/GamificationDashboard';
import { DailyMissions } from '@/components/gamification/DailyMissions';
import { AchievementsWidget } from '@/components/gamification/AchievementsWidget';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Brain, 
  MessageCircle, 
  BarChart3, 
  Lightbulb, 
  TrendingUp,
  Target,
  Sparkles,
  ChevronRight,
  Clock,
  Heart,
  Zap,
  Trophy
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import Link from 'next/link';

// Greeting based on time of day
const getGreeting = () => {
  const hour = new Date().getHours();
  if (hour < 12) return { text: 'Buenos días', icon: '🌅' };
  if (hour < 18) return { text: 'Buenas tardes', icon: '☀️' };
  return { text: 'Buenas noches', icon: '🌙' };
};

// Welcome card with personalization
const WelcomeCard = ({ userProfile }: { userProfile: any }) => {
  const greeting = getGreeting();
  const firstName = userProfile?.fullName?.split(' ')[0] ?? 'Usuario';

  return (
    <Card className="col-span-full bg-gradient-to-br from-primary/10 via-primary/5 to-transparent border-primary/20 overflow-hidden relative">
      <CardHeader className="relative z-10">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-2xl font-headline flex items-center gap-2">
              <span>{greeting.icon}</span>
              {greeting.text}, {firstName}
            </CardTitle>
            <CardDescription className="text-base mt-2">
              {format(new Date(), 'EEEE, dd \'de\' MMMM', { locale: es })}
            </CardDescription>
          </div>
          <div className="text-6xl opacity-20">
            <Brain className="h-16 w-16" />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">
          Tu bienestar mental es nuestra prioridad. ¿Cómo te sientes hoy?
        </p>
      </CardContent>
    </Card>
  );
};

// Quick actions card
const QuickActionsCard = () => {
  const actions = [
    {
      icon: MessageCircle,
      label: 'Chat con Aurora',
      description: 'Habla con tu psicóloga virtual',
      href: '/chat',
      color: 'text-blue-500',
      bgColor: 'bg-blue-500/10 hover:bg-blue-500/20'
    },
    {
      icon: BarChart3,
      label: 'Registrar Ánimo',
      description: 'Anota cómo te sientes',
      href: '/mood-tracker',
      color: 'text-green-500',
      bgColor: 'bg-green-500/10 hover:bg-green-500/20'
    },
    {
      icon: Lightbulb,
      label: 'Recomendaciones',
      description: 'Tips personalizados',
      href: '/recommendations',
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-500/10 hover:bg-yellow-500/20'
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-primary" />
          Acciones Rápidas
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {actions.map((action, index) => (
          <Button
            key={action.href}
            variant="ghost"
            className={cn(
              "w-full justify-start h-auto p-4 group transition-all duration-200",
              action.bgColor
            )}
            asChild
          >
            <a href={action.href}>
              <div className="flex items-center gap-3 w-full">
                <div className={cn(
                  "p-2 rounded-lg transition-transform group-hover:scale-110",
                  action.bgColor.replace('hover:', '')
                )}>
                  <action.icon className={cn("h-5 w-5", action.color)} />
                </div>
                <div className="flex-1 text-left">
                  <div className="font-medium">{action.label}</div>
                  <div className="text-sm text-muted-foreground">
                    {action.description}
                  </div>
                </div>
                <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:translate-x-1 transition-transform" />
              </div>
            </a>
          </Button>
        ))}
      </CardContent>
    </Card>
  );
};

// Usage statistics card
const UsageStatsCard = ({ 
  chatMessagesRemaining, 
  chatMessagesLimit, 
  articlesRemaining, 
  articlesLimit 
}: {
  chatMessagesRemaining: number;
  chatMessagesLimit: number;
  articlesRemaining: number;
  articlesLimit: number;
}) => {
  const chatPercentage = ((chatMessagesLimit - chatMessagesRemaining) / chatMessagesLimit) * 100;
  const articlesPercentage = ((articlesLimit - articlesRemaining) / articlesLimit) * 100;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5 text-primary" />
          Uso de Funciones
        </CardTitle>
        <CardDescription>
          Tu progreso de hoy
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Chat messages */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Mensajes de Chat</span>
            <Badge variant="secondary">
              {chatMessagesLimit - chatMessagesRemaining}/{chatMessagesLimit}
            </Badge>
          </div>
          <Progress value={chatPercentage} className="h-2" />
          <p className="text-xs text-muted-foreground">
            {chatMessagesRemaining} mensajes restantes
          </p>
        </div>

        {/* Articles */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Artículos Generados</span>
            <Badge variant="secondary">
              {articlesLimit - articlesRemaining}/{articlesLimit}
            </Badge>
          </div>
          <Progress value={articlesPercentage} className="h-2" />
          <p className="text-xs text-muted-foreground">
            {articlesRemaining} artículos restantes
          </p>
        </div>

        {/* Upgrade CTA */}
        {(chatMessagesRemaining < 2 || articlesRemaining < 1) && (
          <Button size="sm" className="w-full" asChild>
            <a href="/subscribe">
              <Sparkles className="h-4 w-4 mr-2" />
              Actualizar a Premium
            </a>
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

// Daily tip card with better visual design
const DailyTipCard = ({ tip }: { tip: { title: string; content: string } | null }) => {
  return (
    <Card className="border-amber-200/20 bg-gradient-to-br from-amber-50/50 to-yellow-50/50 dark:from-amber-950/20 dark:to-yellow-950/20">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-amber-800 dark:text-amber-200">
          <Lightbulb className="h-5 w-5" />
          Consejo del Día
        </CardTitle>
      </CardHeader>
      <CardContent>
        {tip ? (
          <div className="space-y-2">
            <h4 className="font-semibold text-amber-900 dark:text-amber-100">
              {tip.title}
            </h4>
            <p className="text-sm text-amber-800/80 dark:text-amber-200/80 leading-relaxed">
              {tip.content}
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="h-4 bg-amber-200/40 rounded animate-pulse" />
            <div className="h-3 bg-amber-200/40 rounded animate-pulse" />
            <div className="h-3 bg-amber-200/40 rounded w-3/4 animate-pulse" />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Recent activity card
const RecentActivityCard = () => {
  const activities = [
    {
      action: 'Conversación con Aurora',
      time: '2 horas',
      icon: MessageCircle,
      color: 'text-blue-500'
    },
    {
      action: 'Registro de ánimo: Tranquilo',
      time: '1 día',
      icon: Heart,
      color: 'text-green-500'
    },
    {
      action: 'Artículo leído: Manejo del estrés',
      time: '2 días',
      icon: Lightbulb,
      color: 'text-yellow-500'
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5 text-primary" />
          Actividad Reciente
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {activities.map((activity, index) => (
          <div key={`activity-${index}-${activity.action.slice(0, 10)}`} className="flex items-center gap-3">
            <div className={cn(
              "p-2 rounded-full bg-muted/50",
              activity.color
            )}>
              <activity.icon className="h-4 w-4" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium">{activity.action}</p>
              <p className="text-xs text-muted-foreground">hace {activity.time}</p>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

// Weekly progress card
const WeeklyProgressCard = () => {
  const progress = [
    { day: 'L', value: 80, completed: true },
    { day: 'M', value: 60, completed: true },
    { day: 'M', value: 90, completed: true },
    { day: 'J', value: 75, completed: true },
    { day: 'V', value: 50, completed: false },
    { day: 'S', value: 0, completed: false },
    { day: 'D', value: 0, completed: false },
  ];

  // Función para generar estilo de altura dinámico
  const getProgressBarStyle = (value: number) => ({ height: `${value}%` });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-primary" />
          Progreso Semanal
        </CardTitle>
        <CardDescription>
          Tu actividad en los últimos 7 días
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-end gap-2">
          {progress.map((day, index) => (
            <div key={`progress-${day.day}-${index}`} className="flex flex-col items-center gap-2">
              <div className="relative w-8 h-16 bg-muted rounded-full overflow-hidden">
                <div 
                  className={cn(
                    "absolute bottom-0 w-full rounded-full transition-all duration-500",
                    day.completed ? "bg-primary" : "bg-muted-foreground/30"
                  )}
                  data-height={day.value}
                  style={getProgressBarStyle(day.value)}
                />
              </div>
              <span className="text-xs font-medium text-muted-foreground">
                {day.day}
              </span>
            </div>
          ))}
        </div>
        <div className="mt-4 p-3 bg-muted/30 rounded-lg">
          <p className="text-sm text-center">
            <span className="font-semibold text-primary">4 de 7</span> días completados esta semana
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

// Main enhanced dashboard
export default function EnhancedDashboard() {
  const { userProfile } = useAuth();

  const [dailyTip, setDailyTip] = useState<{ title: string; content: string } | null>(null);

  useEffect(() => {
    // Simular carga de tip diario
    setTimeout(() => {
      setDailyTip({
        title: "Respira conscientemente",
        content: "Dedica 5 minutos al día a la respiración consciente. Inhala por 4 segundos, mantén por 4, exhala por 6. Esta práctica reduce el estrés y mejora tu concentración."
      });
    }, 1500);
  }, []);

  return (
    <div className="space-y-6 p-6 max-w-7xl mx-auto">
      {/* Welcome section */}
      <div className="grid grid-cols-1 gap-6">
        <WelcomeCard userProfile={userProfile} />
      </div>

      {/* Tabs for different dashboard views */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Vista General
          </TabsTrigger>
          <TabsTrigger value="gamification" className="flex items-center gap-2">
            <Trophy className="h-4 w-4" />
            Progreso y Logros
          </TabsTrigger>
          <TabsTrigger value="activities" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Actividades
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Gamification compact widget */}
          <GamificationDashboard compact />

          {/* Main grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {/* Quick actions */}
            <QuickActionsCard />

            {/* Usage limits card with real functionality */}
            <UsageLimitsCard />

            {/* Daily missions compact */}
            <DailyMissions compact showProgress={false} />

            {/* Daily tip */}
            <DailyTipCard tip={dailyTip} />

            {/* Recent activity */}
            <RecentActivityCard />

            {/* Achievements widget */}
            <AchievementsWidget compact maxItems={2} />

            {/* Weekly progress */}
            <WeeklyProgressCard />
          </div>

          {/* Quick mood check */}
          <Card className="bg-gradient-to-r from-green-50/50 to-blue-50/50 dark:from-green-950/20 dark:to-blue-950/20 border-green-200/20">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold mb-2">¿Cómo te sientes hoy?</h3>
                  <p className="text-sm text-muted-foreground">
                    Registra tu estado de ánimo para un mejor seguimiento
                  </p>
                </div>
                <Button asChild>
                  <Link href="/mood-tracker">
                    <Heart className="h-4 w-4 mr-2" />
                    Registrar Ánimo
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Gamification Tab */}
        <TabsContent value="gamification" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <GamificationDashboard />
            </div>
            <div className="space-y-6">
              <DailyMissions compact />
              <AchievementsWidget compact />
            </div>
          </div>
        </TabsContent>

        {/* Activities Tab */}
        <TabsContent value="activities" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="h-5 w-5 text-blue-500" />
                  Sesiones de Chat
                </CardTitle>
                <CardDescription>
                  Conversa con Aurora para apoyo emocional
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Mensajes enviados hoy</span>
                  <Badge variant="secondary">
                    {userProfile?.dailyChatCount ?? 0}
                  </Badge>
                </div>
                <Button asChild className="w-full">
                  <Link href="/chat">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Iniciar Chat
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-red-500" />
                  Seguimiento del Ánimo
                </CardTitle>
                <CardDescription>
                  Registra tu estado emocional diario
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Registros esta semana</span>
                  <Badge variant="secondary">
                    {userProfile?.moodEntries?.length ?? 0}
                  </Badge>
                </div>
                <Button asChild className="w-full">
                  <Link href="/mood-tracker">
                    <Heart className="h-4 w-4 mr-2" />
                    Registrar Ánimo
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-500" />
                  Contenido Personalizado
                </CardTitle>
                <CardDescription>
                  Artículos y recomendaciones para ti
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Artículos generados</span>
                  <Badge variant="secondary">
                    {userProfile?.articlesRead ?? 0}
                  </Badge>
                </div>
                <Button asChild className="w-full">
                  <Link href="/recommendations">
                    <Lightbulb className="h-4 w-4 mr-2" />
                    Ver Recomendaciones
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-yellow-600" />
                  Logros y Progreso
                </CardTitle>
                <CardDescription>
                  Sigue tu progreso y desbloquea logros
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Logros desbloqueados</span>
                  <Badge variant="secondary">
                    {userProfile?.unlockedAchievements?.length ?? 0}
                  </Badge>
                </div>
                <Button asChild className="w-full">
                  <Link href="/achievements">
                    <Trophy className="h-4 w-4 mr-2" />
                    Ver Logros
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
