package com.menteencalma.app.di;

import com.menteencalma.app.data.monitoring.DatabaseMonitoringService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideDatabaseMonitoringServiceFactory implements Factory<DatabaseMonitoringService> {
  @Override
  public DatabaseMonitoringService get() {
    return provideDatabaseMonitoringService();
  }

  public static AppModule_ProvideDatabaseMonitoringServiceFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static DatabaseMonitoringService provideDatabaseMonitoringService() {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideDatabaseMonitoringService());
  }

  private static final class InstanceHolder {
    private static final AppModule_ProvideDatabaseMonitoringServiceFactory INSTANCE = new AppModule_ProvideDatabaseMonitoringServiceFactory();
  }
}
