
'use client';

import React from 'react';
import { Card, CardH<PERSON>er, Card<PERSON><PERSON>le, CardContent, CardDescription } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { cn } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useGamification } from '@/hooks/use-gamification';
import { 
  Trophy, 
  Star, 
  Lock, 
  Flame,
  Target,
  Gift,
  CheckCircle2,
  Clock
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

// Tipo para Achievement
interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlocked: boolean;
  progress?: number;
  maxProgress?: number;
  unlockedAt?: string;
}

// Componente para mostrar un logro individual
const AchievementCard = ({
  achievement,
  compact = false
}: {
  achievement: Achievement;
  compact?: boolean
}) => {
  const isUnlocked = achievement.unlocked;
  const hasProgress = achievement.progress !== undefined && achievement.maxProgress !== undefined;
  const progressPercentage = hasProgress ? (achievement.progress / achievement.maxProgress) * 100 : 100;

  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Card className={cn(
            "transition-all duration-300 overflow-hidden group cursor-pointer",
            isUnlocked 
              ? "border-primary/50 shadow-lg shadow-primary/10 hover:shadow-primary/20" 
              : "border-muted-foreground/20 bg-muted/30 hover:bg-muted/50",
            compact && "h-full"
          )}>
            <CardHeader className={cn("text-center", compact ? "pb-2" : "pb-4")}>
              {/* Icono del logro */}
              <div className={cn(
                "mx-auto flex items-center justify-center rounded-full mb-2 transition-all group-hover:scale-110",
                compact ? "h-12 w-12" : "h-16 w-16",
                isUnlocked 
                  ? "bg-primary/10 text-primary" 
                  : "bg-muted text-muted-foreground"
              )}>
                {isUnlocked ? (
                  <span className={cn("text-2xl", compact && "text-xl")}>
                    {achievement.icon}
                  </span>
                ) : (
                  <Lock className={cn("h-6 w-6", compact && "h-5 w-5")} />
                )}
              </div>

              {/* Recompensa flotante */}
              {isUnlocked && !compact && (
                <div className="absolute top-2 right-2 bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-bold px-2 py-1 rounded-full flex items-center gap-1 shadow-lg">
                  <Gift className="h-3 w-3" />
                  +{achievement.xp} XP
                </div>
              )}

              {/* Título y descripción */}
              <CardTitle className={cn(
                "transition-colors",
                compact ? "text-base" : "text-lg",
                isUnlocked ? "text-foreground" : "text-muted-foreground"
              )}>
                {achievement.title}
              </CardTitle>
              
              {!compact && (
                <CardDescription className="text-sm">
                  {achievement.description}
                </CardDescription>
              )}
            </CardHeader>

            {/* Contenido adicional */}
            {!compact && (
              <CardContent className="pt-0">
                {/* Barra de progreso para logros parciales */}
                {hasProgress && !isUnlocked && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Progreso</span>
                      <span className="font-medium">
                        {achievement.progress}/{achievement.maxProgress}
                      </span>
                    </div>
                    <Progress value={progressPercentage} className="h-2" />
                  </div>
                )}

                {/* Información de recompensa */}
                <div className="flex items-center justify-between mt-4 pt-4 border-t border-muted">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Star className="h-4 w-4" />
                    {achievement.xp} XP
                  </div>
                  {isUnlocked && achievement.unlockedAt && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <CheckCircle2 className="h-3 w-3" />
                      {format(new Date(achievement.unlockedAt), "d MMM", { locale: es })}
                    </div>
                  )}
                </div>
              </CardContent>
            )}
          </Card>
        </TooltipTrigger>
        <TooltipContent>
          <div className="space-y-1">
            <p className="font-semibold">{achievement.title}</p>
            <p className="text-sm">{achievement.description}</p>
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <span>Recompensa: {achievement.xp} XP</span>
              {hasProgress && !isUnlocked && (
                <span>Progreso: {achievement.progress}/{achievement.maxProgress}</span>
              )}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Componente para el feed de actividad
const ActivityFeedItem = ({ achievement }: { achievement: Achievement }) => (
  <Card className="p-4">
    <div className="flex items-center gap-4">
      <div className="flex-shrink-0 p-3 bg-primary text-primary-foreground rounded-full">
        <Trophy className="h-5 w-5" />
      </div>
      <div className="flex-grow">
        <p className="font-semibold text-sm">¡Logro desbloqueado!</p>
        <p className="text-sm text-muted-foreground">
          Has obtenido <span className="font-bold text-primary">{achievement.title}</span> 
          {' '}y ganado {achievement.xp} puntos de experiencia.
        </p>
      </div>
      <div className="text-right">
        <Badge variant="secondary" className="mb-1">
          +{achievement.xp} XP
        </Badge>
        {achievement.unlockedAt && (
          <p className="text-xs text-muted-foreground">
            {format(new Date(achievement.unlockedAt), "d MMM yyyy", { locale: es })}
          </p>
        )}
      </div>
    </div>
  </Card>
);

export default function AchievementsPage() {
  const {
    achievements,
    unlockedAchievements,
    level,
    xp,
    xpForNextLevel,
    streak,
    loading
  } = useGamification();

  const progressPercentage = ((xp % (xpForNextLevel / level)) / (xpForNextLevel / level)) * 100;
  const recentAchievements = unlockedAchievements
    .filter(a => a.unlockedAt)
    .sort((a, b) => new Date(b.unlockedAt!).getTime() - new Date(a.unlockedAt!).getTime())
    .slice(0, 5);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 space-y-6">
        <div className="space-y-2">
          <div className="h-8 w-64 bg-muted animate-pulse rounded"></div>
          <div className="h-4 w-96 bg-muted animate-pulse rounded"></div>
        </div>
        
        <Card className="animate-pulse">
          <CardHeader>
            <div className="h-6 w-32 bg-muted rounded"></div>
            <div className="h-4 w-48 bg-muted rounded"></div>
          </CardHeader>
          <CardContent>
            <div className="h-3 w-full bg-muted rounded"></div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }, (_, i) => `skeleton-achievement-${i}`).map((skeletonId) => (
            <Card key={skeletonId} className="animate-pulse">
              <CardHeader className="text-center">
                <div className="h-16 w-16 bg-muted rounded-full mx-auto mb-4"></div>
                <div className="h-5 w-32 bg-muted rounded mx-auto mb-2"></div>
                <div className="h-4 w-48 bg-muted rounded mx-auto"></div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <Trophy className="h-8 w-8 text-primary" />
          Logros y Progreso
        </h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Desbloquea logros completando actividades y tareas. Cada logro te otorga experiencia 
          y te ayuda a subir de nivel en tu viaje de bienestar mental.
        </p>
      </div>

      {/* Estadísticas del usuario */}
      <Card className="bg-gradient-to-r from-primary/10 via-primary/5 to-transparent border-primary/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-6 w-6 text-primary" />
                Nivel {level}
              </CardTitle>
              <CardDescription>
                {xp} XP total · {unlockedAchievements.length} logros desbloqueados
              </CardDescription>
            </div>
            <div className="text-right space-y-2">
              <div className="flex items-center gap-4">
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Flame className="h-3 w-3" />
                  {streak} días de racha
                </Badge>
                <Badge variant="outline">
                  {Math.round(progressPercentage)}% al siguiente nivel
                </Badge>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progreso al nivel {level + 1}</span>
              <span>{xp % (xpForNextLevel / level)} / {xpForNextLevel / level} XP</span>
            </div>
            <Progress value={progressPercentage} className="h-3" />
          </div>
        </CardContent>
      </Card>

      {/* Tabs principales */}
      <Tabs defaultValue="all" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all" className="flex items-center gap-2">
            <Trophy className="h-4 w-4" />
            Todos ({achievements.length})
          </TabsTrigger>
          <TabsTrigger value="unlocked" className="flex items-center gap-2">
            <CheckCircle2 className="h-4 w-4" />
            Desbloqueados ({unlockedAchievements.length})
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Actividad reciente
          </TabsTrigger>
        </TabsList>

        {/* Todos los logros */}
        <TabsContent value="all" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {achievements.map((achievement) => (
              <AchievementCard key={achievement.id} achievement={achievement} />
            ))}
          </div>
        </TabsContent>

        {/* Logros desbloqueados */}
        <TabsContent value="unlocked" className="space-y-6">
          {unlockedAchievements.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {unlockedAchievements.map((achievement) => (
                <AchievementCard key={achievement.id} achievement={achievement} />
              ))}
            </div>
          ) : (
            <Card className="text-center py-12">
              <CardContent>
                <Trophy className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">¡Aún no has desbloqueado logros!</h3>
                <p className="text-muted-foreground mb-6">
                  Completa tareas diarias y usa la aplicación para desbloquear tus primeros logros.
                </p>
                <Button className="flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Ver tareas diarias
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Actividad reciente */}
        <TabsContent value="activity" className="space-y-4">
          {recentAchievements.length > 0 ? (
            <div className="space-y-4">
              {recentAchievements.map((achievement) => (
                <ActivityFeedItem key={achievement.id} achievement={achievement} />
              ))}
            </div>
          ) : (
            <Card className="text-center py-12">
              <CardContent>
                <Clock className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No hay actividad reciente</h3>
                <p className="text-muted-foreground">
                  Los logros que desbloquees aparecerán aquí.
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}