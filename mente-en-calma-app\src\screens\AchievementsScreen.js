import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  Modal
} from 'react-native';
import { useAuth } from '../contexts/SupabaseAuthContext';
import { ACHIEVEMENTS_LIST, getLevel, getXPForNextLevel, checkUnlockedAchievements } from '../types/gamification';

const { width } = Dimensions.get('window');

// Función auxiliar para obtener el nombre de la categoría
const getCategoryName = (category) => {
  switch (category) {
    case 'all': return 'Todos';
    case 'wellness': return 'Bienestar';
    case 'productivity': return 'Productividad';
    case 'engagement': return 'Compromiso';
    case 'social': return 'Social';
    default: return 'Todos';
  }
};

// Componente separado para evitar re-renderizado
const AchievementCard = ({ achievement, isUnlocked, onPress }) => (
  <TouchableOpacity
    style={[styles.achievementCard, isUnlocked && styles.achievementCardUnlocked]}
    onPress={() => onPress(achievement)}
    activeOpacity={0.7}
  >
    <View style={styles.achievementHeader}>
      <View style={[styles.achievementIcon, isUnlocked && styles.achievementIconUnlocked]}>
        <Text style={styles.achievementEmoji}>{achievement.icon}</Text>
      </View>
      {isUnlocked && (
        <View style={styles.rewardBadge}>
          <Text style={styles.rewardText}>+{achievement.reward}</Text>
        </View>
      )}
    </View>
    <Text style={[styles.achievementTitle, !isUnlocked && styles.achievementTitleLocked]}>
      {achievement.title}
    </Text>
    <Text style={[styles.achievementDescription, !isUnlocked && styles.achievementDescriptionLocked]}>
      {achievement.description}
    </Text>
  </TouchableOpacity>
);

AchievementCard.propTypes = {
  achievement: PropTypes.shape({
    id: PropTypes.string.isRequired,
    icon: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    reward: PropTypes.number.isRequired,
  }).isRequired,
  isUnlocked: PropTypes.bool.isRequired,
  onPress: PropTypes.func.isRequired,
};

const AchievementsScreen = ({ navigation }) => {
  const { user, userProfile, saveUserProfile } = useAuth();
  const [showAchievementDetail, setShowAchievementDetail] = useState(null);
  const [unlockedAchievements, setUnlockedAchievements] = useState([]);

  useEffect(() => {
    if (userProfile) {
      setUnlockedAchievements(userProfile.unlockedAchievements || []);
      
      // Verificar nuevos logros desbloqueados
      const userStats = {
        chatMessages: userProfile.chatMessages || 0,
        focusSessions: userProfile.focusSessionsHistory?.length || 0,
        articlesRead: userProfile.articlesRead || 0,
        currentStreak: userProfile.currentStreak || 0,
      };
      
      const newlyUnlocked = checkUnlockedAchievements(userProfile, userStats);
      
      if (newlyUnlocked.length > 0) {
        // Actualizar logros desbloqueados
        const updatedProfile = {
          ...userProfile,
          unlockedAchievements: [...(userProfile.unlockedAchievements || []), ...newlyUnlocked.map(a => a.id)]
        };
        
        saveUserProfile(updatedProfile);
        setUnlockedAchievements(updatedProfile.unlockedAchievements);
      }
    }
  }, [userProfile, saveUserProfile]);

  const currentLevel = getLevel(userProfile?.xp || 0);
  const xpForNext = getXPForNextLevel(currentLevel);
  const currentXP = userProfile?.xp || 0;
  const progressPercentage = ((currentXP % xpForNext) / xpForNext) * 100;

  const categories = ['all', 'wellness', 'productivity', 'engagement', 'social'];
  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredAchievements = selectedCategory === 'all' 
    ? ACHIEVEMENTS_LIST 
    : ACHIEVEMENTS_LIST.filter(a => a.category === selectedCategory);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header con nivel y progreso */}
        <View style={styles.header}>
          <View style={styles.profileSection}>
            <View style={styles.avatarContainer}>
              <Text style={styles.avatarEmoji}>🧘‍♀️</Text>
            </View>
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>
                {userProfile?.displayName || user?.email?.split('@')[0] || 'Usuario'}
              </Text>
              <Text style={styles.levelText}>Nivel {currentLevel}</Text>
            </View>
          </View>
          
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{userProfile?.xp || 0}</Text>
              <Text style={styles.statLabel}>XP Total</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{userProfile?.calmaCoins || 0}</Text>
              <Text style={styles.statLabel}>CalmaCoins</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{unlockedAchievements.length}</Text>
              <Text style={styles.statLabel}>Logros</Text>
            </View>
          </View>

          {/* Barra de progreso XP */}
          <View style={styles.progressContainer}>
            <Text style={styles.progressLabel}>
              Progreso al Nivel {currentLevel + 1}: {Math.round(progressPercentage)}%
            </Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, { width: `${progressPercentage}%` }]} />
            </View>
            <Text style={styles.progressText}>
              {currentXP % xpForNext} / {xpForNext} XP
            </Text>
          </View>
        </View>

        {/* Filtros de categoría */}
        <View style={styles.categoryContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {categories.map(category => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryButton,
                  selectedCategory === category && styles.categoryButtonActive
                ]}
                onPress={() => setSelectedCategory(category)}
              >
                <Text style={[
                  styles.categoryButtonText,
                  selectedCategory === category && styles.categoryButtonTextActive                ]}>
                  {getCategoryName(category)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Grid de logros */}
        <View style={styles.achievementsGrid}>
          {filteredAchievements.map(achievement => (            <AchievementCard
              key={achievement.id}
              achievement={achievement}
              isUnlocked={unlockedAchievements.includes(achievement.id)}
              onPress={setShowAchievementDetail}
            />
          ))}
        </View>
      </ScrollView>

      {/* Modal de detalle de logro */}
      <Modal
        visible={showAchievementDetail !== null}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowAchievementDetail(null)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {showAchievementDetail && (
              <>
                <View style={styles.modalIcon}>
                  <Text style={styles.modalEmoji}>{showAchievementDetail.icon}</Text>
                </View>
                <Text style={styles.modalTitle}>{showAchievementDetail.title}</Text>
                <Text style={styles.modalDescription}>{showAchievementDetail.description}</Text>
                <View style={styles.modalRewards}>
                  <View style={styles.rewardItem}>
                    <Text style={styles.rewardLabel}>XP:</Text>
                    <Text style={styles.rewardValue}>{showAchievementDetail.xp}</Text>
                  </View>
                  <View style={styles.rewardItem}>
                    <Text style={styles.rewardLabel}>CalmaCoins:</Text>
                    <Text style={styles.rewardValue}>{showAchievementDetail.reward}</Text>
                  </View>
                </View>
                <TouchableOpacity
                  style={styles.modalButton}
                  onPress={() => setShowAchievementDetail(null)}
                >
                  <Text style={styles.modalButtonText}>Cerrar</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

AchievementsScreen.propTypes = {
  navigation: PropTypes.object.isRequired,
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  avatarContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#e3f2fd',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  avatarEmoji: {
    fontSize: 30,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 4,
  },
  levelText: {
    fontSize: 16,
    color: '#4A90E2',
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
    paddingVertical: 15,
    backgroundColor: '#f1f5f9',
    borderRadius: 12,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#64748b',
    textTransform: 'uppercase',
  },
  progressContainer: {
    marginTop: 10,
  },
  progressLabel: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 8,
    textAlign: 'center',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#e2e8f0',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4A90E2',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    color: '#64748b',
    textAlign: 'center',
  },
  categoryContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  categoryButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#e2e8f0',
    borderRadius: 20,
    marginRight: 10,
  },
  categoryButtonActive: {
    backgroundColor: '#4A90E2',
  },
  categoryButtonText: {
    fontSize: 14,
    color: '#64748b',
    fontWeight: '500',
  },
  categoryButtonTextActive: {
    color: '#fff',
  },
  achievementsGrid: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  achievementCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    opacity: 0.6,
  },
  achievementCardUnlocked: {
    opacity: 1,
    borderWidth: 2,
    borderColor: '#4A90E2',
  },
  achievementHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  achievementIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#f1f5f9',
    justifyContent: 'center',
    alignItems: 'center',
  },
  achievementIconUnlocked: {
    backgroundColor: '#e3f2fd',
  },
  achievementEmoji: {
    fontSize: 24,
  },
  rewardBadge: {
    backgroundColor: '#fbbf24',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  rewardText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 8,
  },
  achievementTitleLocked: {
    color: '#94a3b8',
  },
  achievementDescription: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
  achievementDescriptionLocked: {
    color: '#cbd5e1',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 24,
    margin: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#e3f2fd',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalEmoji: {
    fontSize: 40,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 12,
    textAlign: 'center',
  },
  modalDescription: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
  },
  modalRewards: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 24,
  },
  rewardItem: {
    alignItems: 'center',
  },
  rewardLabel: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 4,
  },
  rewardValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4A90E2',
  },
  modalButton: {
    backgroundColor: '#4A90E2',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});

export default AchievementsScreen;
