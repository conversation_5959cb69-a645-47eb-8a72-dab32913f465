'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useGamification } from '@/hooks/use-gamification';
import { 
  Trophy, 
  Star, 
  Gift,
  ChevronRight,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

interface AchievementsWidgetProps {
  compact?: boolean;
  maxItems?: number;
}

export const AchievementsWidget: React.FC<AchievementsWidgetProps> = ({ 
  compact = false,
  maxItems = 3 
}) => {
  const {
    achievements,
    unlockedAchievements,
    loading
  } = useGamification();

  const recentAchievements = unlockedAchievements
    .filter(a => a.unlockedAt)
    .sort((a, b) => new Date(b.unlockedAt!).getTime() - new Date(a.unlockedAt!).getTime())
    .slice(0, maxItems);

  const totalXP = unlockedAchievements.reduce((sum, achievement) => sum + achievement.xp, 0);
  const completionPercentage = achievements.length > 0 ? (unlockedAchievements.length / achievements.length) * 100 : 0;

  // Función para generar estilo de ancho dinámico
  const getProgressWidthStyle = (percentage: number) => ({
    width: `${Math.min(100, Math.max(0, percentage))}%`
  });

  if (loading) {
    return (
      <Card className="animate-pulse">
        <CardHeader>
          <div className="h-5 w-32 bg-muted rounded"></div>
          <div className="h-3 w-48 bg-muted rounded"></div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: maxItems }, (_, i) => `achievement-skeleton-${i}`).map((skeletonId) => (
              <div key={skeletonId} className="h-16 bg-muted rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (compact) {
    return (
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Trophy className="h-5 w-5 text-yellow-600" />
              Logros
            </CardTitle>
            <Badge variant="secondary">
              {unlockedAchievements.length}/{achievements.length}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          {recentAchievements.length > 0 ? (
            <div className="space-y-2">
              {recentAchievements.map((achievement) => (
                <div
                  key={achievement.id}
                  className="flex items-center gap-2 p-2 rounded-lg bg-yellow-50 border border-yellow-200"
                >
                  <div className="text-lg">{achievement.icon}</div>
                  <div className="flex-grow min-w-0">
                    <p className="font-medium text-sm truncate">{achievement.title}</p>
                    <p className="text-xs text-muted-foreground">
                      +{achievement.xp} XP
                    </p>
                  </div>
                  <Gift className="h-4 w-4 text-yellow-600 flex-shrink-0" />
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4">
              <Trophy className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                ¡Completa actividades para desbloquear logros!
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-yellow-600" />
              Logros y Progreso
            </CardTitle>
            <CardDescription>
              {unlockedAchievements.length} de {achievements.length} logros desbloqueados
            </CardDescription>
          </div>
          <Link href="/achievements">
            <Button variant="ghost" size="sm">
              Ver todos
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </Link>
        </div>
        <div className="mt-4 space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progreso general</span>
            <span>{Math.round(completionPercentage)}% completado</span>
          </div>
          <div className="w-full bg-muted rounded-full h-2">
            <div 
              className={cn(
                "bg-gradient-to-r from-yellow-400 to-orange-400 h-2 rounded-full transition-all duration-500",
                completionPercentage > 0 && "min-w-[2px]"
              )}
              style={getProgressWidthStyle(completionPercentage)}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Stats row */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Star className="h-4 w-4 text-yellow-600" />
              <span className="font-bold text-lg text-yellow-700">{totalXP}</span>
            </div>
            <p className="text-xs text-yellow-600 font-medium">XP Total</p>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Trophy className="h-4 w-4 text-blue-600" />
              <span className="font-bold text-lg text-blue-700">{unlockedAchievements.length}</span>
            </div>
            <p className="text-xs text-blue-600 font-medium">Desbloqueados</p>
          </div>
        </div>

        {/* Recent achievements */}
        {recentAchievements.length > 0 ? (
          <div className="space-y-3">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <Sparkles className="h-4 w-4 text-primary" />
              Logros recientes
            </h4>
            {recentAchievements.map((achievement) => (
              <div
                key={achievement.id}
                className="flex items-center gap-3 p-3 rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200"
              >
                <div className="flex-shrink-0 p-2 bg-white rounded-full border border-yellow-300">
                  <span className="text-lg">{achievement.icon}</span>
                </div>
                <div className="flex-grow min-w-0">
                  <p className="font-medium text-sm text-yellow-800 truncate">
                    {achievement.title}
                  </p>
                  <p className="text-xs text-yellow-600">
                    {achievement.unlockedAt && 
                      format(new Date(achievement.unlockedAt), "d MMM", { locale: es })
                    }
                  </p>
                </div>
                <div className="flex-shrink-0 flex items-center gap-1">
                  <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-700">
                    +{achievement.xp} XP
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6">
            <Trophy className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
            <p className="font-medium text-sm mb-1">¡Aún no has desbloqueado logros!</p>
            <p className="text-xs text-muted-foreground mb-4">
              Completa actividades diarias para comenzar tu colección
            </p>
            <Link href="/achievements">
              <Button size="sm" variant="outline">
                <Trophy className="h-4 w-4 mr-2" />
                Explorar Logros
              </Button>
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
