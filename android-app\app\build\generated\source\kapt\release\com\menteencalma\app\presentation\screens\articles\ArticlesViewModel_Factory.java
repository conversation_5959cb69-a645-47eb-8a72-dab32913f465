package com.menteencalma.app.presentation.screens.articles;

import com.google.firebase.firestore.FirebaseFirestore;
import com.menteencalma.app.data.service.CloudFunctionsService;
import com.menteencalma.app.domain.repository.AuthRepository;
import com.menteencalma.app.domain.repository.DatabaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ArticlesViewModel_Factory implements Factory<ArticlesViewModel> {
  private final Provider<CloudFunctionsService> cloudFunctionsServiceProvider;

  private final Provider<DatabaseRepository> databaseRepositoryProvider;

  private final Provider<AuthRepository> authRepositoryProvider;

  private final Provider<FirebaseFirestore> firestoreProvider;

  public ArticlesViewModel_Factory(Provider<CloudFunctionsService> cloudFunctionsServiceProvider,
      Provider<DatabaseRepository> databaseRepositoryProvider,
      Provider<AuthRepository> authRepositoryProvider,
      Provider<FirebaseFirestore> firestoreProvider) {
    this.cloudFunctionsServiceProvider = cloudFunctionsServiceProvider;
    this.databaseRepositoryProvider = databaseRepositoryProvider;
    this.authRepositoryProvider = authRepositoryProvider;
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public ArticlesViewModel get() {
    return newInstance(cloudFunctionsServiceProvider.get(), databaseRepositoryProvider.get(), authRepositoryProvider.get(), firestoreProvider.get());
  }

  public static ArticlesViewModel_Factory create(
      Provider<CloudFunctionsService> cloudFunctionsServiceProvider,
      Provider<DatabaseRepository> databaseRepositoryProvider,
      Provider<AuthRepository> authRepositoryProvider,
      Provider<FirebaseFirestore> firestoreProvider) {
    return new ArticlesViewModel_Factory(cloudFunctionsServiceProvider, databaseRepositoryProvider, authRepositoryProvider, firestoreProvider);
  }

  public static ArticlesViewModel newInstance(CloudFunctionsService cloudFunctionsService,
      DatabaseRepository databaseRepository, AuthRepository authRepository,
      FirebaseFirestore firestore) {
    return new ArticlesViewModel(cloudFunctionsService, databaseRepository, authRepository, firestore);
  }
}
