package com.menteencalma.app.data.service;

import com.google.firebase.functions.FirebaseFunctions;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CloudFunctionsService_Factory implements Factory<CloudFunctionsService> {
  private final Provider<FirebaseFunctions> functionsProvider;

  private final Provider<MockCloudFunctionsService> mockServiceProvider;

  public CloudFunctionsService_Factory(Provider<FirebaseFunctions> functionsProvider,
      Provider<MockCloudFunctionsService> mockServiceProvider) {
    this.functionsProvider = functionsProvider;
    this.mockServiceProvider = mockServiceProvider;
  }

  @Override
  public CloudFunctionsService get() {
    return newInstance(functionsProvider.get(), mockServiceProvider.get());
  }

  public static CloudFunctionsService_Factory create(Provider<FirebaseFunctions> functionsProvider,
      Provider<MockCloudFunctionsService> mockServiceProvider) {
    return new CloudFunctionsService_Factory(functionsProvider, mockServiceProvider);
  }

  public static CloudFunctionsService newInstance(FirebaseFunctions functions,
      MockCloudFunctionsService mockService) {
    return new CloudFunctionsService(functions, mockService);
  }
}
