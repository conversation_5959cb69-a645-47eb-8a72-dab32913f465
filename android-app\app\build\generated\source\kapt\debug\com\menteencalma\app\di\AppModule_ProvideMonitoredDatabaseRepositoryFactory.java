package com.menteencalma.app.di;

import com.menteencalma.app.data.monitoring.DatabaseMonitoringService;
import com.menteencalma.app.domain.repository.DatabaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata({
    "com.menteencalma.app.di.MonitoredDatabase",
    "com.menteencalma.app.di.FirebaseDatabase"
})
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideMonitoredDatabaseRepositoryFactory implements Factory<DatabaseRepository> {
  private final Provider<DatabaseRepository> actualRepositoryProvider;

  private final Provider<DatabaseMonitoringService> monitoringServiceProvider;

  public AppModule_ProvideMonitoredDatabaseRepositoryFactory(
      Provider<DatabaseRepository> actualRepositoryProvider,
      Provider<DatabaseMonitoringService> monitoringServiceProvider) {
    this.actualRepositoryProvider = actualRepositoryProvider;
    this.monitoringServiceProvider = monitoringServiceProvider;
  }

  @Override
  public DatabaseRepository get() {
    return provideMonitoredDatabaseRepository(actualRepositoryProvider.get(), monitoringServiceProvider.get());
  }

  public static AppModule_ProvideMonitoredDatabaseRepositoryFactory create(
      Provider<DatabaseRepository> actualRepositoryProvider,
      Provider<DatabaseMonitoringService> monitoringServiceProvider) {
    return new AppModule_ProvideMonitoredDatabaseRepositoryFactory(actualRepositoryProvider, monitoringServiceProvider);
  }

  public static DatabaseRepository provideMonitoredDatabaseRepository(
      DatabaseRepository actualRepository, DatabaseMonitoringService monitoringService) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideMonitoredDatabaseRepository(actualRepository, monitoringService));
  }
}
