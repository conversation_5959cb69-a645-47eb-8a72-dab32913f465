package com.menteencalma.app.presentation.viewmodels;

import com.google.firebase.firestore.FirebaseFirestore;
import com.menteencalma.app.domain.repository.AuthRepository;
import com.menteencalma.app.domain.repository.DatabaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MoodTrackerViewModel_Factory implements Factory<MoodTrackerViewModel> {
  private final Provider<DatabaseRepository> databaseRepositoryProvider;

  private final Provider<AuthRepository> authRepositoryProvider;

  private final Provider<FirebaseFirestore> firestoreProvider;

  public MoodTrackerViewModel_Factory(Provider<DatabaseRepository> databaseRepositoryProvider,
      Provider<AuthRepository> authRepositoryProvider,
      Provider<FirebaseFirestore> firestoreProvider) {
    this.databaseRepositoryProvider = databaseRepositoryProvider;
    this.authRepositoryProvider = authRepositoryProvider;
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public MoodTrackerViewModel get() {
    return newInstance(databaseRepositoryProvider.get(), authRepositoryProvider.get(), firestoreProvider.get());
  }

  public static MoodTrackerViewModel_Factory create(
      Provider<DatabaseRepository> databaseRepositoryProvider,
      Provider<AuthRepository> authRepositoryProvider,
      Provider<FirebaseFirestore> firestoreProvider) {
    return new MoodTrackerViewModel_Factory(databaseRepositoryProvider, authRepositoryProvider, firestoreProvider);
  }

  public static MoodTrackerViewModel newInstance(DatabaseRepository databaseRepository,
      AuthRepository authRepository, FirebaseFirestore firestore) {
    return new MoodTrackerViewModel(databaseRepository, authRepository, firestore);
  }
}
