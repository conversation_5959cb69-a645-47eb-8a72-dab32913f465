#!/bin/bash

echo "🧹 LIM<PERSON>ANDO DEPENDENCIAS Y MIGRANDO A SUPABASE..."

# Limpiar node_modules y package-lock
echo "📦 Limpiando node_modules..."
rm -rf node_modules
rm -f package-lock.json

# Limpiar caché de npm
echo "🗑️ Limpiando caché de npm..."
npm cache clean --force

# Limpiar caché de Expo
echo "📱 Limpiando caché de Expo..."
npx expo r -c

# Reinstalar dependencias
echo "⬇️ Reinstalando dependencias..."
npm install

echo "✅ MIGRACIÓN A SUPABASE COMPLETADA!"
echo ""
echo "📋 PRÓXIMOS PASOS:"
echo "1. Ejecutar: npm start"
echo "2. Probar autenticación"
echo "3. Verificar que todas las funciones usen Supabase"
echo ""
echo "🚀 LA APP ANDROID AHORA USA 100% SUPABASE!"
