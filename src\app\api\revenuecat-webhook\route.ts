import { NextResponse } from 'next/server';
import { supabaseServer } from '@/lib/supabase-server';

// Función para determinar la plataforma basada en el store
function getPlatformFromStore(store: string): string {
  if (store === 'STRIPE') return 'web';
  if (store === 'PLAY_STORE') return 'android';
  return 'ios';
}

// Función para determinar el tipo de plan
function getPlanType(productId: string): string {
  const isAnnual = productId.includes('annual') ?? productId.includes('year');
  return isAnnual ? 'annual' : 'monthly';
}

// Función para obtener la fecha de expiración
function getExpirationDate(premiumEntitlement: any, event: any): string | null {
  if (premiumEntitlement?.expires_date) {
    return new Date(premiumEntitlement.expires_date).toISOString();
  }
  if (event.expiration_date_ms) {
    return new Date(event.expiration_date_ms).toISOString();
  }
  return null;
}

// Función para manejar activación de suscripción
async function handleSubscriptionActivation(event: any, premiumEntitlement: any) {
  const productId = event.product_id ?? '';
  const platform = getPlatformFromStore(event.store);
  const planType = getPlanType(productId);
  const expirationDate = getExpirationDate(premiumEntitlement, event);

  await supabaseServer
    .from('profiles')
    .update({
      subscriptionStatus: 'premium',
      subscriptionPlatform: platform,
      subscriptionPlanType: planType,
      subscriptionExpiresAt: expirationDate
    })
    .eq('uid', event.app_user_id);

  console.log('✅ Subscription activated for user:', event.app_user_id, 'Platform:', platform);
}

// Función para manejar expiración de suscripción
async function handleSubscriptionExpiration(userId: string) {
  await supabaseServer
    .from('profiles')
    .update({
      subscriptionStatus: 'free',
      subscriptionPlatform: null,
      subscriptionPlanType: 'free'
    })
    .eq('uid', userId);

  console.log('❌ Subscription expired/billing issue for user:', userId);
}

// Función para manejar reactivación de suscripción
async function handleSubscriptionReactivation(event: any, premiumEntitlement: any) {
  const productId = event.product_id ?? '';
  const platform = getPlatformFromStore(event.store);
  const planType = getPlanType(productId);
  const expirationDate = getExpirationDate(premiumEntitlement, event);

  await supabaseServer
    .from('profiles')
    .update({
      subscriptionStatus: 'premium',
      subscriptionPlatform: platform,
      subscriptionPlanType: planType,
      subscriptionExpiresAt: expirationDate
    })
    .eq('uid', event.app_user_id);

  console.log('🔄 Subscription reactivated for user:', event.app_user_id, 'Platform:', platform);
}

export async function POST(req: Request) {
  try {
    const event = await req.json();
    
    console.log('🔔 RevenueCat webhook received:', {
      type: event.type,
      appUserId: event.app_user_id,
      store: event.store
    });

    // Verificar que tenemos los datos necesarios
    if (!event.app_user_id) {
      console.error('❌ No app_user_id in webhook');
      return NextResponse.json({ error: 'Missing app_user_id' }, { status: 400 });
    }

    // Verificar entitlements específicos de RevenueCat
    const entitlements = event.entitlements ?? {};
    const premiumEntitlement = entitlements['premium_access']; // Usar el entitlement real

    switch (event.type) {
      case 'INITIAL_PURCHASE':
      case 'RENEWAL':
      case 'NON_RENEWING_PURCHASE':
        await handleSubscriptionActivation(event, premiumEntitlement);
        break;

      case 'CANCELLATION':
        // No cambiar el estado inmediatamente en cancelación
        // El usuario mantiene acceso hasta que expire
        console.log('⚠️ Subscription cancelled (but still active until expiry):', event.app_user_id);
        break;

      case 'EXPIRATION':
      case 'BILLING_ISSUE':
        await handleSubscriptionExpiration(event.app_user_id);
        break;

      case 'UNCANCELLATION':
        await handleSubscriptionReactivation(event, premiumEntitlement);
        break;

      default:
        console.log('ℹ️ Unhandled webhook event type:', event.type);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('❌ Error processing RevenueCat webhook:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}
