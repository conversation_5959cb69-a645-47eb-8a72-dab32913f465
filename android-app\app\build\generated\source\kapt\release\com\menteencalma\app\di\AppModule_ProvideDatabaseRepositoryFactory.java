package com.menteencalma.app.di;

import com.menteencalma.app.domain.repository.DatabaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("com.menteencalma.app.di.MonitoredDatabase")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideDatabaseRepositoryFactory implements Factory<DatabaseRepository> {
  private final Provider<DatabaseRepository> repositoryProvider;

  public AppModule_ProvideDatabaseRepositoryFactory(
      Provider<DatabaseRepository> repositoryProvider) {
    this.repositoryProvider = repositoryProvider;
  }

  @Override
  public DatabaseRepository get() {
    return provideDatabaseRepository(repositoryProvider.get());
  }

  public static AppModule_ProvideDatabaseRepositoryFactory create(
      Provider<DatabaseRepository> repositoryProvider) {
    return new AppModule_ProvideDatabaseRepositoryFactory(repositoryProvider);
  }

  public static DatabaseRepository provideDatabaseRepository(DatabaseRepository repository) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideDatabaseRepository(repository));
  }
}
