package com.menteencalma.app.data.migration;

import com.menteencalma.app.domain.repository.DatabaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseMigrationService_Factory implements Factory<DatabaseMigrationService> {
  private final Provider<DatabaseRepository> sourceRepositoryProvider;

  public DatabaseMigrationService_Factory(Provider<DatabaseRepository> sourceRepositoryProvider) {
    this.sourceRepositoryProvider = sourceRepositoryProvider;
  }

  @Override
  public DatabaseMigrationService get() {
    return newInstance(sourceRepositoryProvider.get());
  }

  public static DatabaseMigrationService_Factory create(
      Provider<DatabaseRepository> sourceRepositoryProvider) {
    return new DatabaseMigrationService_Factory(sourceRepositoryProvider);
  }

  public static DatabaseMigrationService newInstance(DatabaseRepository sourceRepository) {
    return new DatabaseMigrationService(sourceRepository);
  }
}
