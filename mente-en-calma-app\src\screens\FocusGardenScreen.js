import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  Dimensions,
  Modal
} from 'react-native';
import { useAuth } from '../contexts/SupabaseAuthContext';
import { FOCUS_TIERS } from '../types/gamification';

const { width, height } = Dimensions.get('window');

const FocusGardenScreen = ({ navigation }) => {
  const { user, userProfile, saveUserProfile } = useAuth();
  const [selectedTierId, setSelectedTierId] = useState('brote');
  const [timeLeft, setTimeLeft] = useState(15 * 60); // 15 minutos por defecto
  const [isActive, setIsActive] = useState(false);
  const [mode, setMode] = useState('idle'); // 'idle', 'planting', 'focus', 'break'
  const [plantedTrees, setPlantedTrees] = useState([]);
  const [pendingPlant, setPendingPlant] = useState(null);
  const [showUnlockModal, setShowUnlockModal] = useState(false);
  const [tierToUnlock, setTierToUnlock] = useState(null);
  const intervalRef = useRef(null);

  const currentTier = FOCUS_TIERS.find(t => t.id === selectedTierId) || FOCUS_TIERS[1];
  const unlockedTiers = userProfile?.unlockedFocusTiers || ['hierba', 'brote', 'arbol'];

  useEffect(() => {
    if (userProfile?.plantedTrees) {
      setPlantedTrees(userProfile.plantedTrees);
    }
  }, [userProfile]);

  useEffect(() => {
    if (isActive && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => prev - 1);
      }, 1000);
    } else if (timeLeft === 0 && isActive) {
      completeSession();
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isActive, timeLeft]);

  const completeSession = () => {
    setIsActive(false);
    
    if (mode === 'focus' && pendingPlant) {
      // Completar la sesión exitosamente
      const updatedTrees = plantedTrees.map(tree => 
        tree.id === pendingPlant.id ? { ...tree, status: 'alive' } : tree
      );
      
      const updatedProfile = {
        ...userProfile,
        plantedTrees: updatedTrees,
        calmaCoins: (userProfile.calmaCoins || 0) + currentTier.reward,
        xp: (userProfile.xp || 0) + (currentTier.reward * 2), // XP adicional
        focusSessionsHistory: [
          ...(userProfile.focusSessionsHistory || []),
          {
            date: new Date().toISOString(),
            tierId: selectedTierId,
            completed: true,
            duration: currentTier.duration
          }
        ]
      };

      setPlantedTrees(updatedTrees);
      saveUserProfile(updatedProfile);
      
      Alert.alert(
        '¡Sesión Completada!', 
        `Has ganado ${currentTier.reward} CalmaCoins y ${currentTier.reward * 2} XP. Tu planta ha crecido en el jardín.`,
        [{ text: 'OK', onPress: () => {
          setPendingPlant(null);
          setMode('break');
          setTimeLeft(Math.floor(currentTier.duration / 5)); // Descanso corto
        }}]
      );
    }
  };

  const handleTierSelect = (tierId) => {
    if (isActive) return;
    
    if (!unlockedTiers.includes(tierId)) {
      const tier = FOCUS_TIERS.find(t => t.id === tierId);
      setTierToUnlock(tier);
      setShowUnlockModal(true);
      return;
    }
    
    const tier = FOCUS_TIERS.find(t => t.id === tierId);
    setSelectedTierId(tierId);
    setTimeLeft(tier.duration);
    setMode('idle');
  };

  const handleStartPlanting = () => {
    if (!user || !userProfile) return;
    setMode('planting');
    Alert.alert('Plantar', 'Toca en cualquier lugar del jardín para plantar tu semilla.');
  };

  const handleGardenPress = (event) => {
    if (mode !== 'planting') return;

    const { locationX, locationY } = event.nativeEvent;
    const gardenWidth = width - 40; // Considerando padding
    const gardenHeight = 200;
    
    const x = (locationX / gardenWidth) * 100;
    const y = (locationY / gardenHeight) * 100;

    const newPlant = {
      id: new Date().toISOString(),
      tierId: selectedTierId,
      date: new Date().toISOString(),
      position: { x, y },
      status: 'growing'
    };

    const updatedTrees = [...plantedTrees, newPlant];
    setPlantedTrees(updatedTrees);
    setPendingPlant(newPlant);
    
    setMode('focus');
    setIsActive(true);
  };

  const handleUnlockTier = () => {
    if (!tierToUnlock || !user || !userProfile) return;
    
    const currentCoins = userProfile.calmaCoins || 0;
    if (currentCoins < tierToUnlock.unlockCost) {
      Alert.alert('Monedas Insuficientes', `Necesitas ${tierToUnlock.unlockCost} CalmaCoins para desbloquear este nivel.`);
      return;
    }

    const updatedProfile = {
      ...userProfile,
      calmaCoins: currentCoins - tierToUnlock.unlockCost,
      unlockedFocusTiers: [...unlockedTiers, tierToUnlock.id]
    };

    saveUserProfile(updatedProfile);
    setShowUnlockModal(false);
    Alert.alert('¡Nivel Desbloqueado!', `Ahora puedes usar sesiones de ${tierToUnlock.name}.`);
  };

  const abandonSession = () => {
    Alert.alert(
      'Abandonar Sesión',
      '¿Estás seguro? Tu planta se marchitará.',
      [
        { text: 'Cancelar', style: 'cancel' },
        { text: 'Abandonar', style: 'destructive', onPress: () => {
          setIsActive(false);
          if (pendingPlant) {
            const updatedTrees = plantedTrees.map(tree => 
              tree.id === pendingPlant.id ? { ...tree, status: 'dead' } : tree
            );
            setPlantedTrees(updatedTrees);
            
            const updatedProfile = {
              ...userProfile,
              plantedTrees: updatedTrees
            };
            saveUserProfile(updatedProfile);
          }
          setPendingPlant(null);
          setMode('idle');
          setTimeLeft(currentTier.duration);
        }}
      ]
    );
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const renderGarden = () => {
    return (
      <TouchableOpacity 
        style={styles.garden}
        onPress={handleGardenPress}
        activeOpacity={mode === 'planting' ? 0.7 : 1}
      >
        <Text style={styles.gardenTitle}>🌸 Tu Jardín de Concentración 🌸</Text>
        <View style={styles.gardenArea}>
          {plantedTrees.map((tree) => {
            const tier = FOCUS_TIERS.find(t => t.id === tree.tierId);
            const icon = tree.status === 'dead' ? '🥀' : tier?.icon || '🌱';
            
            return (
              <Text
                key={tree.id}
                style={[
                  styles.plantedTree,
                  {
                    left: `${tree.position.x}%`,
                    top: `${tree.position.y}%`,
                  }
                ]}
              >
                {icon}
              </Text>
            );
          })}
          
          {mode === 'planting' && (
            <Text style={styles.plantingIndicator}>
              Toca aquí para plantar tu {currentTier.icon}
            </Text>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Focus Garden</Text>
          <View style={styles.statsContainer}>
            <View style={styles.stat}>
              <Text style={styles.statLabel}>CalmaCoins</Text>
              <Text style={styles.statValue}>{userProfile?.calmaCoins || 0}</Text>
            </View>
            <View style={styles.stat}>
              <Text style={styles.statLabel}>XP</Text>
              <Text style={styles.statValue}>{userProfile?.xp || 0}</Text>
            </View>
          </View>
        </View>

        {/* Timer */}
        <View style={styles.timerContainer}>
          <Text style={styles.timerText}>{formatTime(timeLeft)}</Text>
          <Text style={styles.timerLabel}>
            {mode === 'focus' ? `Enfocándote con ${currentTier.name}` : 
             mode === 'break' ? 'Tiempo de descanso' : 
             `Listo para ${currentTier.name}`}
          </Text>
        </View>

        {/* Garden */}
        {renderGarden()}

        {/* Focus Tiers */}
        <View style={styles.tiersContainer}>
          <Text style={styles.sectionTitle}>Niveles de Concentración</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {FOCUS_TIERS.map((tier) => {
              const isUnlocked = unlockedTiers.includes(tier.id);
              const isSelected = selectedTierId === tier.id;
              
              return (
                <TouchableOpacity
                  key={tier.id}
                  style={[
                    styles.tierCard,
                    isSelected && styles.tierCardSelected,
                    !isUnlocked && styles.tierCardLocked
                  ]}
                  onPress={() => handleTierSelect(tier.id)}
                  disabled={isActive}
                >
                  <Text style={styles.tierIcon}>{tier.icon}</Text>
                  <Text style={styles.tierName}>{tier.name}</Text>
                  <Text style={styles.tierDuration}>{Math.round(tier.duration / 60)} min</Text>
                  <Text style={styles.tierReward}>+{tier.reward} coins</Text>
                  {!isUnlocked && (
                    <Text style={styles.tierCost}>{tier.unlockCost} coins</Text>
                  )}
                </TouchableOpacity>
              );
            })}
          </ScrollView>
        </View>

        {/* Controls */}
        <View style={styles.controlsContainer}>
          {mode === 'idle' && (
            <TouchableOpacity 
              style={styles.primaryButton} 
              onPress={handleStartPlanting}
            >
              <Text style={styles.buttonText}>Comenzar Sesión</Text>
            </TouchableOpacity>
          )}
          
          {mode === 'focus' && (
            <TouchableOpacity 
              style={styles.dangerButton} 
              onPress={abandonSession}
            >
              <Text style={styles.buttonText}>Abandonar Sesión</Text>
            </TouchableOpacity>
          )}
          
          {mode === 'break' && (
            <TouchableOpacity 
              style={styles.primaryButton} 
              onPress={() => {
                setMode('idle');
                setTimeLeft(currentTier.duration);
              }}
            >
              <Text style={styles.buttonText}>Nueva Sesión</Text>
            </TouchableOpacity>
          )}
        </View>
      </ScrollView>

      {/* Modal para desbloquear niveles */}
      <Modal
        visible={showUnlockModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowUnlockModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalTitle}>Desbloquear Nivel</Text>
            {tierToUnlock && (
              <>
                <Text style={styles.modalIcon}>{tierToUnlock.icon}</Text>
                <Text style={styles.modalText}>
                  ¿Quieres desbloquear {tierToUnlock.name}?
                </Text>
                <Text style={styles.modalCost}>
                  Costo: {tierToUnlock.unlockCost} CalmaCoins
                </Text>
                <Text style={styles.modalBalance}>
                  Tienes: {userProfile?.calmaCoins || 0} CalmaCoins
                </Text>
                
                <View style={styles.modalButtons}>
                  <TouchableOpacity 
                    style={styles.modalButtonCancel}
                    onPress={() => setShowUnlockModal(false)}
                  >
                    <Text style={styles.modalButtonCancelText}>Cancelar</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity 
                    style={styles.modalButtonConfirm}
                    onPress={handleUnlockTier}
                  >
                    <Text style={styles.modalButtonConfirmText}>Desbloquear</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

FocusGardenScreen.propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func.isRequired,
    goBack: PropTypes.func,
  }).isRequired,
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'white',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2d3748',
  },
  statsContainer: {
    flexDirection: 'row',
    gap: 16,
  },
  stat: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    color: '#718096',
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4a5568',
  },
  timerContainer: {
    alignItems: 'center',
    padding: 30,
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginTop: 10,
    borderRadius: 12,
  },
  timerText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#3182ce',
  },
  timerLabel: {
    fontSize: 16,
    color: '#718096',
    marginTop: 8,
  },
  garden: {
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 12,
    overflow: 'hidden',
  },
  gardenTitle: {
    textAlign: 'center',
    fontSize: 18,
    fontWeight: 'bold',
    padding: 16,
    backgroundColor: '#e6fffa',
    color: '#2d3748',
  },
  gardenArea: {
    height: 200,
    backgroundColor: '#f0fff4',
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  plantedTree: {
    position: 'absolute',
    fontSize: 24,
  },
  plantingIndicator: {
    textAlign: 'center',
    color: '#718096',
    fontSize: 16,
  },
  tiersContainer: {
    marginHorizontal: 20,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 12,
  },
  tierCard: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    marginRight: 12,
    alignItems: 'center',
    minWidth: 100,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  tierCardSelected: {
    borderColor: '#3182ce',
    backgroundColor: '#ebf8ff',
  },
  tierCardLocked: {
    opacity: 0.6,
    backgroundColor: '#f7fafc',
  },
  tierIcon: {
    fontSize: 32,
    marginBottom: 4,
  },
  tierName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2d3748',
  },
  tierDuration: {
    fontSize: 12,
    color: '#718096',
  },
  tierReward: {
    fontSize: 12,
    color: '#38a169',
    fontWeight: 'bold',
  },
  tierCost: {
    fontSize: 10,
    color: '#e53e3e',
    marginTop: 2,
  },
  controlsContainer: {
    padding: 20,
  },
  primaryButton: {
    backgroundColor: '#3182ce',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  dangerButton: {
    backgroundColor: '#e53e3e',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    margin: 20,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2d3748',
    marginBottom: 16,
  },
  modalIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  modalText: {
    fontSize: 16,
    color: '#4a5568',
    textAlign: 'center',
    marginBottom: 8,
  },
  modalCost: {
    fontSize: 16,
    color: '#e53e3e',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  modalBalance: {
    fontSize: 14,
    color: '#718096',
    marginBottom: 24,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButtonCancel: {
    backgroundColor: '#e2e8f0',
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  modalButtonCancelText: {
    color: '#4a5568',
    fontWeight: 'bold',
  },
  modalButtonConfirm: {
    backgroundColor: '#3182ce',
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  modalButtonConfirmText: {
    color: 'white',
    fontWeight: 'bold',
  },
});

export default FocusGardenScreen;
