'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { BookOpen, Feather, Save, Loader2, CheckCircle, Trash2, Eye, Sparkles, Share2 } from 'lucide-react';
import type { Article as ArticleType, GenerateArticleOutput } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import PremiumModal from '@/components/ui/premium-modal';
import { useAuth } from '@/contexts/supabase-auth-context';
import { supabase } from '@/lib/supabase';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { useRouter } from 'next/navigation';

const articleRequestSchema = z.object({
  topic: z.string().min(3, "El tema debe tener al menos 3 caracteres."),
});
type ArticleRequestFormData = z.infer<typeof articleRequestSchema>;

export default function ArticlesPage() {
  const { toast } = useToast();
  const { user } = useAuth();
  const router = useRouter();
    const [generatedArticle, setGeneratedArticle] = useState<(GenerateArticleOutput & { topic: string }) | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [limitReached, setLimitReached] = useState(false);
  const [showPremiumModal, setShowPremiumModal] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  
  const [savedArticles, setSavedArticles] = useState<ArticleType[]>([]);
  const [isLoadingSaved, setIsLoadingSaved] = useState(true);
  const [articleToDelete, setArticleToDelete] = useState<ArticleType | null>(null);
  const [viewingArticle, setViewingArticle] = useState<ArticleType | null>(null);

  const form = useForm<ArticleRequestFormData>({
    resolver: zodResolver(articleRequestSchema),
    defaultValues: { topic: '' },
  });
  
  const isGeneratedArticleSaved = useMemo(() => {
    if (!generatedArticle) return false;
    return savedArticles.some(saved => saved.title === generatedArticle.title && saved.topic === generatedArticle.topic);
  }, [generatedArticle, savedArticles]);
  // Function to load saved articles
  const loadSavedArticles = useCallback(async () => {
    if (!user) {
      setIsLoadingSaved(false);
      setSavedArticles([]);
      return;
    }

    try {
      setIsLoadingSaved(true);
      const { data, error } = await supabase
        .from('articles')
        .select('*')
        .eq('user_id', user.id)
        .order('saved_at', { ascending: false });

      if (error) {
        console.error('❌ Error fetching articles:', error);
        toast({ 
          title: "Error", 
          description: "No se pudieron cargar los artículos guardados.", 
          variant: "destructive" 
        });
        return;
      }

      const articlesData = data?.map(article => ({
        id: article.id,
        title: article.title,
        content: article.content,
        topic: article.topic,
        savedAt: new Date(article.saved_at),
      })) ?? [];

      console.log('✅ Loaded saved articles:', articlesData.length);
      setSavedArticles(articlesData);
    } catch (error) {
      console.error('❌ Error loading articles:', error);
      toast({ 
        title: "Error", 
        description: "No se pudieron cargar los artículos guardados.", 
        variant: "destructive" 
      });
    } finally {
      setIsLoadingSaved(false);
    }
  }, [user, toast]);
  useEffect(() => {
    loadSavedArticles();
  }, [loadSavedArticles]);
  // Helper para renderizar la sección de artículos guardados
  const renderSavedArticlesSection = () => {
    if (isLoadingSaved) {
      return (
        <div className="grid gap-4 md:grid-cols-2">          {Array.from({ length: 4 }, (_, i) => (
            <Card key={`loading-skeleton-article-${i}`}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-5/6" />
              </CardContent>
            </Card>
          ))}
        </div>
      );
    }
    
    if (savedArticles.length === 0) {
      return (
        <Card>
          <CardContent className="text-center py-8">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              No tienes artículos guardados aún. ¡Genera tu primer artículo!
            </p>
          </CardContent>
        </Card>
      );
    }
    
    return (
      <div className="grid gap-4 md:grid-cols-2">
        {savedArticles.map((article) => (
          <Card key={article.id}>
            <CardHeader>
              <CardTitle className="text-lg">{article.title}</CardTitle>              <CardDescription>
                {article.savedAt ? new Date(article.savedAt).toLocaleDateString('es-ES', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                }) : 'Fecha no disponible'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-300 line-clamp-3">
                {article.content.substring(0, 200)}...
              </p>
            </CardContent>
            <CardFooter className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewingArticle(article)}
              >
                <Eye className="mr-2 h-4 w-4" />
                Ver
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleShare(article)}
              >
                <Share2 className="mr-2 h-4 w-4" />
                Compartir
              </Button>              <Button
                variant="destructive"
                size="sm"
                onClick={() => setArticleToDelete(article)}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Eliminar
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  };

  // Helper para renderizar el botón de guardar
  const renderSaveButton = () => {
    if (isSaving) {
      return (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Guardando...
        </>
      );
    }
    
    if (isGeneratedArticleSaved) {
      return (
        <>
          <CheckCircle className="mr-2 h-4 w-4" />
          Guardado
        </>
      );
    }
    
    return (
      <>
        <Save className="mr-2 h-4 w-4" />
        Guardar Artículo
      </>
    );
  };

  const onSubmit: SubmitHandler<ArticleRequestFormData> = async (data) => {
    if (!user) return;
    setIsGenerating(true);
    setGeneratedArticle(null);
    setLimitReached(false);
    
    try {
      // Generar artículo (la verificación de límites se hace en el backend)
      console.log('🤖 Generating article...');
      const response = await fetch('/api/generate-article', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          topic: data.topic,
          uid: user.id,
        }),
      });

      const result = await response.json();
      
      if (!response.ok) {        // Manejar errores específicos del backend
        if (result.error === 'ARTICLE_LIMIT_REACHED') {
          console.log('🚫 Article limit reached');
          setLimitReached(true);
          setShowPremiumModal(true);
          return;
        }
        
        throw new Error(result.message ?? 'Error generando artículo');
      }

      console.log('✅ Article generated:', result);
      setGeneratedArticle({ ...result, topic: data.topic });
        } catch (error: unknown) {
      console.error('❌ Error generating article:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "No se pudo generar el artículo. Inténtalo de nuevo.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSaveArticle = async () => {
    if (!generatedArticle || !user) return;
    
    console.log('🔄 Saving article for user:', user.id);
    setIsSaving(true);
    
    try {
      const { error } = await supabase
        .from('articles')
        .insert({
          user_id: user.id, // This should be a valid UUID from Supabase Auth
          title: generatedArticle.title,
          content: generatedArticle.content,
          topic: generatedArticle.topic,
          saved_at: new Date().toISOString(),
        });

      if (error) {
        console.error('❌ Error saving article:', error);
        console.error('Error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        
        let errorMessage = "Error al guardar el artículo";
        if (error.code === '22P02') {
          errorMessage = "Error de formato en los datos del usuario";
        } else if (error.code === '23505') {
          errorMessage = "Este artículo ya fue guardado";
        }
        
        toast({ 
          title: "Error al guardar", 
          description: errorMessage,
          variant: "destructive" 
        });
        return;
      }

      console.log('✅ Article saved successfully');
      toast({ title: "¡Artículo Guardado!" });
      
      // Refrescar la lista de artículos
      await loadSavedArticles();
      
    } catch (error) {
      console.error('❌ Unexpected error saving article:', error);
      toast({ 
        title: "Error al guardar", 
        description: "Error inesperado al guardar el artículo",
        variant: "destructive" 
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteArticle = async (articleId: string) => {
    if (!user) return;
    try {
      const { error } = await supabase
        .from('articles')
        .delete()
        .eq('id', articleId)
        .eq('user_id', user.id);

      if (error) {
        console.error('❌ Error deleting article:', error);
        toast({ title: "Error al eliminar", variant: "destructive" });
        return;
      }

      console.log('✅ Article deleted successfully');
      toast({ title: "Artículo Eliminado" });
      setArticleToDelete(null);
      
      // Refrescar la lista de artículos
      await loadSavedArticles();
    } catch (error) {
       console.error('❌ Error deleting article:', error);
       toast({ title: "Error al eliminar", variant: "destructive" });
    }
  };
  
  const handleShare = async (article: ArticleType | (GenerateArticleOutput & { topic?: string })) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: article.title,
          text: article.content,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback for browsers that don't support Web Share API
      navigator.clipboard.writeText(`${article.title}\n\n${article.content}`);
      toast({ title: "Copiado al portapapeles" });
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardHeader>
            <CardTitle>Acceso Requerido</CardTitle>
            <CardDescription>
              Necesitas iniciar sesión para generar artículos personalizados.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => router.push('/login')}>
              Iniciar Sesión
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white flex items-center justify-center gap-2">
          <Feather className="h-8 w-8 text-blue-600" />
          Generador de Artículos
        </h1>
        <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Genera artículos personalizados sobre salud mental con IA
        </p>
      </div>

      {/* Formulario de generación */}
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-purple-600" />
            Generar Nuevo Artículo
          </CardTitle>
          <CardDescription>
            Introduce un tema y la IA generará un artículo personalizado para ti.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="topic"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tema del Artículo</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Ej: manejo del estrés, técnicas de relajación..."
                        {...field}
                        disabled={isGenerating}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button 
                type="submit" 
                disabled={isGenerating}
                className="w-full"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generando Artículo...
                  </>
                ) : (
                  <>
                    <Feather className="mr-2 h-4 w-4" />
                    Generar Artículo
                  </>
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Artículo generado */}
      {generatedArticle && (
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-green-600" />
              {generatedArticle.title}
            </CardTitle>
            <CardDescription>
              Tema: {generatedArticle.topic}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="prose max-w-none text-gray-700 dark:text-gray-300">              {generatedArticle.content.split('\n').map((paragraph, index) => (
                <p key={`article-paragraph-${index}-${paragraph.slice(0, 10)}`} className="mb-4">
                  {paragraph}
                </p>
              ))}
            </div>
          </CardContent>
          <CardFooter className="flex gap-2">            <Button onClick={handleSaveArticle} disabled={isSaving || isGeneratedArticleSaved}>
              {renderSaveButton()}
            </Button>
            <Button variant="outline" onClick={() => handleShare(generatedArticle)}>
              <Share2 className="mr-2 h-4 w-4" />
              Compartir
            </Button>
          </CardFooter>
        </Card>
      )}

      {/* Límite alcanzado */}
      {limitReached && (
        <Card className="max-w-2xl mx-auto border-amber-200 bg-amber-50 dark:bg-amber-950">
          <CardHeader>
            <CardTitle className="text-amber-700 dark:text-amber-300">
              Límite de Artículos Alcanzado
            </CardTitle>
            <CardDescription className="text-amber-600 dark:text-amber-400">
              Has generado el máximo de artículos permitidos este mes.
            </CardDescription>
          </CardHeader>
        </Card>
      )}

      {/* Artículos guardados */}
      <div className="max-w-4xl mx-auto">
        <h2 className="text-2xl font-semibold mb-6 flex items-center gap-2">
          <BookOpen className="h-6 w-6 text-blue-600" />
          Mis Artículos Guardados
        </h2>        
        {renderSavedArticlesSection()}
      </div>

      {/* Dialog para ver artículo */}
      <Dialog open={!!viewingArticle} onOpenChange={() => setViewingArticle(null)}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>{viewingArticle?.title}</DialogTitle>            <DialogDescription>
              Tema: {viewingArticle?.topic} • Guardado el {viewingArticle?.savedAt ? format(viewingArticle.savedAt, 'dd/MM/yyyy', { locale: es }) : 'Fecha no disponible'}
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="max-h-[60vh]">
            <div className="prose max-w-none text-gray-700 dark:text-gray-300">              {viewingArticle?.content.split('\n').map((paragraph, index) => (
                <p key={`viewing-paragraph-${index}-${paragraph.slice(0, 10)}`} className="mb-4">
                  {paragraph}
                </p>
              ))}
            </div>
          </ScrollArea>
          <DialogFooter>
            <Button variant="outline" onClick={() => viewingArticle && handleShare(viewingArticle)}>
              <Share2 className="mr-2 h-4 w-4" />
              Compartir
            </Button>
            <DialogClose asChild>
              <Button>Cerrar</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog de confirmación para eliminar */}
      <AlertDialog open={!!articleToDelete} onOpenChange={() => setArticleToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>¿Eliminar artículo?</AlertDialogTitle>            <AlertDialogDescription>
              Esta acción no se puede deshacer. El artículo &quot;{articleToDelete?.title}&quot; será eliminado permanentemente.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (articleToDelete) {
                  handleDeleteArticle(articleToDelete.id);
                }
              }}
              className="bg-red-600 hover:bg-red-700"
            >
              Eliminar
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>      </AlertDialog>

      {/* Modal Premium */}
      <PremiumModal
        isOpen={showPremiumModal}
        onClose={() => setShowPremiumModal(false)}
        triggerFeature="articles"
      />
    </div>
  );
}
