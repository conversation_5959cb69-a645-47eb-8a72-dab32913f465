
'use client';

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Play, Pause, ToyBrick, CheckCircle, Zap, MousePointerClick, BarChart2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/supabase-auth-context';
import { useToast } from '@/hooks/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader as AlertDialogTitleHeader, AlertDialogDescription as AlertDialogDesc, AlertDialogTitle as AlertDialogTitleText } from '@/components/ui/alert-dialog';
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid } from 'recharts';
import { format, isSameDay, startOfWeek } from 'date-fns';
import { es } from 'date-fns/locale';
import { PlantedTree } from '@/types';

// --- CONFIGURACIÓN Y TIPOS ---
const focusTiers = [
  { id: 'hierba', name: 'Hierba', duration: 5 * 60, icon: '🌾', reward: 2, unlockCost: 0 },
  { id: 'brote', name: 'Brote', duration: 15 * 60, icon: '🌱', reward: 5, unlockCost: 0 },
  { id: 'arbol', name: 'Árbol', duration: 25 * 60, icon: '🌳', reward: 10, unlockCost: 0 },
  { id: 'cactus', name: 'Cactus', duration: 20 * 60, icon: '🌵', reward: 8, unlockCost: 50 },
  { id: 'girasol', name: 'Girasol', duration: 30 * 60, icon: '🌻', reward: 15, unlockCost: 150 },
  { id: 'palmera', name: 'Palmera', duration: 40 * 60, icon: '🌴', reward: 20, unlockCost: 250 },
  { id: 'bosque', name: 'Bosque', duration: 45 * 60, icon: '🌲', reward: 25, unlockCost: 350 },
  { id: 'secuoya', name: 'Secuoya', duration: 60 * 60, icon: '🏞️', reward: 50, unlockCost: 500 },
  { id: 'cerezo', name: 'Cerezo', duration: 75 * 60, icon: '🌸', reward: 70, unlockCost: 750 },
  { id: 'arce', name: 'Arce Japonés', duration: 90 * 60, icon: '🍁', reward: 100, unlockCost: 1000 },
  { id: 'fuego', name: 'Árbol de Fuego', duration: 100 * 60, icon: '🔥', reward: 120, unlockCost: 1500 },
  { id: 'montana', name: 'Montaña Mística', duration: 120 * 60, icon: '⛰️', reward: 150, unlockCost: 2500 },
  { id: 'estrella', name: 'Árbol Estelar', duration: 150 * 60, icon: '✨', reward: 200, unlockCost: 4000 },
  { id: 'portal', name: 'Portal Cósmico', duration: 180 * 60, icon: '🌀', reward: 250, unlockCost: 6000 },
  { id: 'jungla', name: 'Corazón de Jungla', duration: 210 * 60, icon: '🌿', reward: 300, unlockCost: 8000 },
  { id: 'galaxia', name: 'Galaxia Contenida', duration: 240 * 60, icon: '🌌', reward: 400, unlockCost: 10000 },
];
const DEAD_TREE_ICON = '🥀';

type TimerMode = 'idle' | 'planting' | 'focus' | 'break';

// --- FUNCIONES AUXILIARES ---
const calculateDayMinutes = (plantedTrees: PlantedTree[], day: Date): number => {
    const daySessions = plantedTrees.filter(tree => 
        tree.status === 'alive' && isSameDay(new Date(tree.date), day)
    );
    
    return daySessions.reduce((sum, session) => {
        const tier = focusTiers.find(t => t.id === session.tierId);
        return sum + (tier ? tier.duration / 60 : 0);
    }, 0);
};

const generateWeeklyStats = (plantedTrees: PlantedTree[]) => {
    const today = new Date();
    const startOfThisWeek = startOfWeek(today, { weekStartsOn: 1 }); // Lunes
    
    return Array.from({ length: 7 }).map((_, i) => {
        const day = new Date(startOfThisWeek);
        day.setDate(day.getDate() + i);
        const totalMinutes = calculateDayMinutes(plantedTrees, day);
        
        return { 
            name: format(day, 'E', { locale: es }), 
            minutos: Math.round(totalMinutes) 
        };
    });
};

// --- COMPONENTE DE ESTADÍSTICAS ---
const FocusStats = ({ plantedTrees }: { plantedTrees: PlantedTree[] }) => {
    const weeklyStats = useMemo(() => 
        generateWeeklyStats(plantedTrees), 
        [plantedTrees]
    );

    const totalMinutesToday = weeklyStats[new Date().getDay() - 1]?.minutos ?? 0;
    const totalTreesPlanted = plantedTrees.filter(t => t.status === 'alive').length;

    return (
        <Card>
          <CardHeader><CardTitle className="flex items-center"><BarChart2 className="mr-2 h-6 w-6 text-primary"/>Tu Progreso</CardTitle><CardDescription>Resumen de tu actividad de enfoque.</CardDescription></CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 gap-4 text-center">
                <div className="p-4 bg-muted/50 rounded-lg"><p className="text-2xl font-bold">{totalMinutesToday}</p><p className="text-sm text-muted-foreground">Minutos hoy</p></div>
                <div className="p-4 bg-muted/50 rounded-lg"><p className="text-2xl font-bold">{totalTreesPlanted}</p><p className="text-sm text-muted-foreground">Árboles plantados</p></div>
            </div>
            <div>
              <h4 className="text-sm font-medium mb-2 text-center text-muted-foreground">Actividad de la Semana (minutos)</h4>
              <ResponsiveContainer width="100%" height={200}>
                <BarChart data={weeklyStats}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="name" fontSize={12} tickLine={false} axisLine={false} />
                  <YAxis fontSize={12} tickLine={false} axisLine={false} width={30}/>
                  <Tooltip cursor={{ fill: 'hsl(var(--muted))' }} contentStyle={{ backgroundColor: 'hsl(var(--background))', border: '1px solid hsl(var(--border))' }} />
                  <Bar dataKey="minutos" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
    );
};


export default function FocusGardenPage() {
  const { user, userProfile, saveUserProfile } = useAuth();
  const { toast } = useToast();
  
  const [selectedTierId, setSelectedTierId] = useState('arbol');
  const [mode, setMode] = useState<TimerMode>('idle');
  const [isActive, setIsActive] = useState(false);
  const [pendingPlant, setPendingPlant] = useState<PlantedTree | null>(null);

  const currentTier = useMemo(() => focusTiers.find(t => t.id === selectedTierId)!, [selectedTierId]);
  
  const [timeLeft, setTimeLeft] = useState(currentTier.duration);
  const [plantedTrees, setPlantedTrees] = useState<PlantedTree[]>([]);
  const [isStoreOpen, setIsStoreOpen] = useState(false);
  const [isAbandonConfirmOpen, setIsAbandonConfirmOpen] = useState(false);
  
  const gardenRef = useRef<HTMLDivElement>(null);

  useEffect(() => { 
    if (userProfile) {
      const treesWithId = (userProfile.plantedTrees ?? []).map((tree, index) => ({
        ...tree,
        id: tree.id ?? `tree-${index}-${tree.date}` // Generar ID si no existe
      }));
      setPlantedTrees(treesWithId);
    } 
  }, [userProfile]);

  const formatTime = (seconds: number) => `${Math.floor(seconds / 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
  
  const progress = useMemo(() => {
    if(mode !== 'focus') return 0;
    const totalDuration = currentTier.duration;
    if(totalDuration === 0) return 0;
    return ((totalDuration - timeLeft) / totalDuration) * 100;
  }, [timeLeft, mode, currentTier]);

  const abandonSession = useCallback(() => {
    if (!user || !userProfile || !pendingPlant) return;
    setIsActive(false);
    const updatedTrees = plantedTrees.map(tree => tree.id === pendingPlant.id ? { ...tree, status: 'dead' as const } : tree);
    setPlantedTrees(updatedTrees);
    saveUserProfile({ ...userProfile, plantedTrees: updatedTrees });
    toast({ title: "Sesión Abandonada", description: "Una planta se ha marchitado en tu jardín.", variant: 'destructive' });
    setPendingPlant(null);
    setMode('idle');
    setTimeLeft(currentTier.duration);
  }, [user, userProfile, pendingPlant, plantedTrees, currentTier, saveUserProfile, toast]);


  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;
    if (isActive && timeLeft > 0) {
      interval = setInterval(() => setTimeLeft(prev => prev - 1), 1000);
    } else if (timeLeft === 0 && isActive) {
      setIsActive(false);
      if (mode === 'focus' && user && userProfile && pendingPlant) {
          const updatedTrees = plantedTrees.map(tree => tree.id === pendingPlant.id ? { ...tree, status: 'alive' as const } : tree);
          setPlantedTrees(updatedTrees);
          saveUserProfile({ ...userProfile, plantedTrees: updatedTrees, calmaCoins: (userProfile.calmaCoins ?? 0) + currentTier.reward });
          toast({ title: "¡Sesión Completada!", description: `Has ganado ${currentTier.reward} CalmaCoins.` });
          setPendingPlant(null);
          setMode('break');
          setTimeLeft(Math.floor(currentTier.duration / 5));
      }
    }
    return () => { if (interval) clearInterval(interval); };
  }, [isActive, timeLeft, mode, user, userProfile, saveUserProfile, plantedTrees, toast, currentTier, pendingPlant]);

  // Función para generar estilos dinámicos de posición
  const getTreePositionStyle = (tree: PlantedTree) => ({
    left: `${tree.position.x}%`,
    top: `${tree.position.y}%`,
    transform: `translate(-50%, -50%) scale(${tree.status === 'growing' ? (progress/100 * 0.8 + 0.2) : 1})`,
    opacity: tree.status === 'growing' ? (progress/100 * 0.9 + 0.1) : 1
  });

  const handleTierSelect = (tierId: string) => {
    if (isActive) return;
    const tier = focusTiers.find(t => t.id === tierId)!;
    setSelectedTierId(tierId);
    setTimeLeft(tier.duration);
    setMode('idle');
  };
  
  const handleStartPlanting = () => {
    setMode('planting');
    toast({ description: "Elige un lugar en tu jardín para plantar." });
  };
  
  const handleGardenClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (mode !== 'planting' || !user || !userProfile || !gardenRef.current) return;

    const gardenRect = gardenRef.current.getBoundingClientRect();
    const x = ((e.clientX - gardenRect.left) / gardenRect.width) * 100;
    const y = ((e.clientY - gardenRect.top) / gardenRect.height) * 100;

    const newPlant: PlantedTree = { id: new Date().toISOString(), tierId: currentTier.id, date: new Date().toISOString(), position: { x, y }, status: 'growing'};
    setPlantedTrees([...plantedTrees, newPlant]);
    setPendingPlant(newPlant);
    
    setMode('focus');
    setIsActive(true);
  };
  
  const handleUnlockTier = (tierId: string, cost: number) => {
    if (!user || !userProfile) return;
    const currentCoins = userProfile.calmaCoins ?? 0;
    if (currentCoins < cost) {
      toast({ title: "Monedas Insuficientes", variant: "destructive" });
      return;
    }
    const currentTiers = userProfile.unlockedFocusTiers || ['brote', 'arbol', 'hierba'];
    if (currentTiers.includes(tierId)) {
        toast({ title: "Ya has desbloqueado este nivel" });
        return;
    }
    
    const updatedProfile = { ...userProfile, calmaCoins: currentCoins - cost, unlockedFocusTiers: [...currentTiers, tierId]};
    saveUserProfile(updatedProfile).then(() => {
      toast({ title: "¡Nivel Desbloqueado!", description: `Ahora puedes iniciar sesiones de ${focusTiers.find(t => t.id === tierId)?.name}.` });
    });
  };

  const unlockedTiers = userProfile?.unlockedFocusTiers ?? ['brote', 'arbol', 'hierba'];
  
  return (
    <div className="grid lg:grid-cols-2 gap-8 items-start">
      <div className="space-y-4">
        <Card className="shadow-lg text-center overflow-hidden">
          <CardHeader className="bg-muted/30"><CardTitle className="text-2xl font-headline">Sesión de Enfoque</CardTitle><CardDescription>Elige una tarea, plántala en tu jardín y concéntrate.</CardDescription></CardHeader>
          <CardContent className="pt-6">
            <Tabs value={selectedTierId} onValueChange={handleTierSelect} className="mb-6"><TabsList>
                {focusTiers.filter(t => unlockedTiers.includes(t.id)).map(tier => (<TabsTrigger key={tier.id} value={tier.id} disabled={isActive}>{tier.name}</TabsTrigger>))}
            </TabsList></Tabs>
            <div className="flex flex-col items-center">
                {mode !== 'focus' ? (
                     <div className="relative mb-4 w-60 h-60 flex flex-col items-center justify-center">
                        <span className="text-8xl">{currentTier.icon}</span>
                        <h2 className="text-5xl font-bold font-mono mt-4">{formatTime(timeLeft)}</h2>
                        <p className="text-muted-foreground text-sm font-medium mt-2">{mode === 'idle' ? 'Listo para empezar' : 'DESCANSO'}</p>
                     </div>
                ) : (
                    <div className="relative mb-4 w-60 h-60 flex flex-col items-center justify-center">
                        <h2 className="text-5xl font-bold font-mono z-10">{formatTime(timeLeft)}</h2>
                        <svg className="absolute w-full h-full" viewBox="0 0 100 100">
                            <circle className="text-muted/20" strokeWidth="7" stroke="currentColor" fill="transparent" r="45" cx="50" cy="50" />
                            <circle className={cn("transition-all duration-500", 'text-primary')} strokeWidth="7" strokeDasharray={2 * Math.PI * 45} strokeDashoffset={2 * Math.PI * 45 * (1 - progress / 100)} strokeLinecap="round" transform="rotate(-90 50 50)" stroke="currentColor" fill="transparent" r="45" cx="50" cy="50" />
                        </svg>
                    </div>
                )}
              <div className="flex items-center gap-4 mt-4">
                {mode === 'idle' && <Button onClick={handleStartPlanting} size="lg" className="w-40"><Play className="mr-2 h-5 w-5"/>Empezar</Button>}
                {mode === 'focus' && <Button onClick={() => setIsActive(!isActive)} size="lg" className="w-40">{isActive ? <><Pause className="mr-2 h-5 w-5"/>Pausar</> : <><Play className="mr-2 h-5 w-5"/>Continuar</>}</Button>}
                {mode === 'break' && <Button onClick={() => { setMode('idle'); setTimeLeft(currentTier.duration); }} size="lg" className="w-40">Nueva Sesión</Button>}
                {isActive && <Button onClick={() => setIsAbandonConfirmOpen(true)} variant="destructive" size="lg">Abandonar</Button>}
              </div>
            </div>
          </CardContent>
        </Card>
        <FocusStats plantedTrees={plantedTrees} />
      </div>
      
      <div className="space-y-4 lg:sticky lg:top-20">
        <Card>
          <CardHeader className="flex flex-row justify-between items-center">
              <div><CardTitle>Tu Jardín</CardTitle><CardDescription>Un reflejo de tu concentración.</CardDescription></div>
              <Button onClick={() => setIsStoreOpen(true)} variant="outline"><Zap className="mr-2 h-4 w-4" />Desbloquear</Button>
          </CardHeader>
          <CardContent 
            ref={gardenRef} 
            className={cn(
              "relative min-h-[50vh] p-0 bg-cover bg-center rounded-lg overflow-hidden border-2 border-green-800/20", 
              mode === 'planting' ? 'cursor-crosshair animate-pulse' : ''
            )} 
            style={{ backgroundImage: "url('/images/fondo-terreno.svg')" }} 
            onClick={handleGardenClick}
          >
            {mode === 'planting' && (
              <div className="absolute inset-0 bg-black/50 text-white flex flex-col items-center justify-center z-20 pointer-events-none">
                <MousePointerClick className="h-8 w-8 mb-2" />
                <p className="font-semibold text-lg">Elige dónde plantar tu {currentTier.name}</p>
              </div>
            )}
            {plantedTrees.map((tree) => (
              <span 
                key={tree.id} 
                className={cn(
                  "absolute text-6xl transition-all duration-500", 
                  tree.status === 'dead' ? 'filter grayscale' : '', 
                  tree.status === 'growing' ? 'animate-pulse' : 'hover:scale-110'
                )} 
                style={getTreePositionStyle(tree)}
              >
                {tree.status === 'dead' ? DEAD_TREE_ICON : focusTiers.find(t => t.id === tree.tierId)?.icon}
              </span>
            ))}
            {plantedTrees.length === 0 && (
              <p className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-muted-foreground bg-background/50 p-2 rounded">
                Tu jardín está esperando.
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      <Dialog open={isStoreOpen} onOpenChange={setIsStoreOpen}>
          <DialogContent>
              <DialogHeader><DialogTitle>Desbloquear Niveles de Enfoque</DialogTitle><DialogDescription>Usa tus CalmaCoins para acceder a sesiones más largas y obtener mejores recompensas.</DialogDescription></DialogHeader>
              <div className="space-y-4 py-4 max-h-[60vh] overflow-y-auto">
                  {focusTiers.filter(t => t.unlockCost > 0).map(tier => (
                      <div key={tier.id} className="flex justify-between items-center p-2 rounded-lg hover:bg-muted">
                          <div className="flex items-center gap-4">
                              <span className="text-4xl">{tier.icon}</span>
                              <div>
                                  <p className="font-semibold">{tier.name} ({tier.duration / 60} min)</p>
                                  <p className="text-sm text-muted-foreground">Recompensa: {tier.reward} monedas</p>
                              </div>
                          </div>
                          {unlockedTiers.includes(tier.id) ? (
                              <Button variant="secondary" disabled><CheckCircle className="mr-2 h-4 w-4"/> Desbloqueado</Button>
                          ) : (
                             <Button onClick={() => handleUnlockTier(tier.id, tier.unlockCost)} disabled={(userProfile?.calmaCoins ?? 0) < tier.unlockCost}>
                                <ToyBrick className="mr-2 h-4 w-4"/> {tier.unlockCost}
                             </Button>
                          )}
                      </div>
                  ))}
              </div>
          </DialogContent>
      </Dialog>
      <AlertDialog open={isAbandonConfirmOpen} onOpenChange={setIsAbandonConfirmOpen}>
          <AlertDialogContent>
              <AlertDialogTitleHeader><AlertDialogTitleText>¿Abandonar Sesión?</AlertDialogTitleText></AlertDialogTitleHeader>
              <AlertDialogDesc>Si abandonas ahora, se plantará un árbol marchito en tu jardín y no recibirás monedas. ¿Estás seguro?</AlertDialogDesc>
              <AlertDialogFooter>
                  <AlertDialogCancel>Continuar Enfocándome</AlertDialogCancel>
                  <AlertDialogAction onClick={abandonSession} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">Abandonar</AlertDialogAction>
              </AlertDialogFooter>
          </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
