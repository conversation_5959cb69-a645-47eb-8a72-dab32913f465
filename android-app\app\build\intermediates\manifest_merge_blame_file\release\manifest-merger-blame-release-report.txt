1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.eligi.menteencalma"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- <PERSON><PERSON>os necesarios -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:6:5-67
12-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:7:5-79
13-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="com.android.vending.BILLING" />
14-->[com.android.billingclient:billing-ktx:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\658f12a736932d07849764a7d0cd2ae4\transformed\billing-ktx-6.1.0\AndroidManifest.xml:9:5-67
14-->[com.android.billingclient:billing-ktx:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\658f12a736932d07849764a7d0cd2ae4\transformed\billing-ktx-6.1.0\AndroidManifest.xml:9:22-64
15
16    <queries>
16-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:12:5-16:15
17        <intent>
17-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:13:9-15:18
18            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
18-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:14:13-91
18-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:14:21-88
19        </intent>
20    </queries>
21
22    <uses-permission android:name="android.permission.WAKE_LOCK" />
22-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
22-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:22-65
23    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
23-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
23-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
24    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
24-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
24-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
25    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" /> <!-- Required by older versions of Google Play services to create IID tokens -->
25-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
25-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
26    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
26-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
26-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:26:22-79
27    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
27-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
27-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
28
29    <permission
29-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
30        android:name="com.eligi.menteencalma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="com.eligi.menteencalma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
34
35    <application
35-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:9:5-38:19
36        android:name="com.menteencalma.app.MenteEnCalmaApplication"
36-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:10:9-48
37        android:allowBackup="true"
37-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:11:9-35
38        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
38-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b7c5e14f30f524dc22cc8925fd453a32\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
39        android:dataExtractionRules="@xml/data_extraction_rules"
39-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:12:9-65
40        android:extractNativeLibs="false"
41        android:fullBackupContent="@xml/backup_rules"
41-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:13:9-54
42        android:icon="@drawable/ic_launcher_legacy"
42-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:14:9-52
43        android:label="@string/app_name"
43-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:15:9-41
44        android:roundIcon="@drawable/ic_launcher_legacy"
44-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:16:9-57
45        android:supportsRtl="true"
45-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:17:9-35
46        android:theme="@style/Theme.MenteEnCalma" >
46-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:18:9-50
47
48        <!-- Actividad principal -->
49        <activity
49-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:22:9-30:20
50            android:name="com.menteencalma.app.MainActivity"
50-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:23:13-41
51            android:exported="true"
51-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:24:13-36
52            android:theme="@style/Theme.MenteEnCalma.Splash" >
52-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:25:13-61
53            <intent-filter>
53-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:26:13-29:29
54                <action android:name="android.intent.action.MAIN" />
54-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:27:17-69
54-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:27:25-66
55
56                <category android:name="android.intent.category.LAUNCHER" />
56-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:28:17-77
56-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:28:27-74
57            </intent-filter>
58        </activity>
59
60        <!-- Configuración para Google Play Billing -->
61        <meta-data
61-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:33:9-36:45
62            android:name="com.google.android.play.billingclient.version"
62-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:34:13-73
63            android:value="6.1.0" />
63-->B:\Mente en calma\android-app\app\src\main\AndroidManifest.xml:35:13-34
64
65        <activity
65-->[com.revenuecat.purchases:purchases-ui:7.3.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\efe1fc2b5d54a20c44165b33db2259bf\transformed\purchases-ui-7.3.5\AndroidManifest.xml:8:9-10:40
66            android:name="com.revenuecat.purchases.ui.revenuecatui.activity.PaywallActivity"
66-->[com.revenuecat.purchases:purchases-ui:7.3.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\efe1fc2b5d54a20c44165b33db2259bf\transformed\purchases-ui-7.3.5\AndroidManifest.xml:9:13-93
67            android:exported="false" />
67-->[com.revenuecat.purchases:purchases-ui:7.3.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\efe1fc2b5d54a20c44165b33db2259bf\transformed\purchases-ui-7.3.5\AndroidManifest.xml:10:13-37
68        <activity
68-->[com.revenuecat.purchases:purchases:7.3.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf9ae7fb5c58b1efeb325a96a48764b0\transformed\purchases-7.3.5\AndroidManifest.xml:10:9-13:75
69            android:name="com.revenuecat.purchases.amazon.purchasing.ProxyAmazonBillingActivity"
69-->[com.revenuecat.purchases:purchases:7.3.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf9ae7fb5c58b1efeb325a96a48764b0\transformed\purchases-7.3.5\AndroidManifest.xml:11:13-97
70            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
70-->[com.revenuecat.purchases:purchases:7.3.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf9ae7fb5c58b1efeb325a96a48764b0\transformed\purchases-7.3.5\AndroidManifest.xml:12:13-96
71            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
71-->[com.revenuecat.purchases:purchases:7.3.5] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf9ae7fb5c58b1efeb325a96a48764b0\transformed\purchases-7.3.5\AndroidManifest.xml:13:13-72
72        <activity
72-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
73            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
73-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
74            android:excludeFromRecents="true"
74-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
75            android:exported="false"
75-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
76            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
76-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
77        <!--
78            Service handling Google Sign-In user revocation. For apps that do not integrate with
79            Google Sign-In, this service will never be started.
80        -->
81        <service
81-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
82            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
82-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
83            android:exported="true"
83-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
84            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
84-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
85            android:visibleToInstantApps="true" />
85-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d3b5d7c1f148cd634fcde4a9e09bc452\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
86        <service
86-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\055fd7523edd902eb5d8a768a41d5707\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:8:9-14:19
87            android:name="com.google.firebase.components.ComponentDiscoveryService"
87-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\055fd7523edd902eb5d8a768a41d5707\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:9:13-84
88            android:directBootAware="true"
88-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
89            android:exported="false" >
89-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\055fd7523edd902eb5d8a768a41d5707\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:10:13-37
90            <meta-data
90-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\055fd7523edd902eb5d8a768a41d5707\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:11:13-13:85
91                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
91-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\055fd7523edd902eb5d8a768a41d5707\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:12:17-119
92                android:value="com.google.firebase.components.ComponentRegistrar" />
92-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\055fd7523edd902eb5d8a768a41d5707\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:13:17-82
93            <meta-data
93-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
94                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
94-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
95                android:value="com.google.firebase.components.ComponentRegistrar" />
95-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
96            <meta-data
96-->[com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:12:13-14:85
97                android:name="com.google.firebase.components:com.google.firebase.functions.ktx.FirebaseFunctionsLegacyRegistrar"
97-->[com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:13:17-129
98                android:value="com.google.firebase.components.ComponentRegistrar" />
98-->[com.google.firebase:firebase-functions-ktx:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9ee38be121e013a5632211dea0128ad8\transformed\firebase-functions-ktx-20.4.0\AndroidManifest.xml:14:17-82
99            <meta-data
99-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
100                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
100-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
101                android:value="com.google.firebase.components.ComponentRegistrar" />
101-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0806107622080c57bf8f19ccebc1cf53\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
102            <meta-data
102-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\81abd97b94442baf319fe3cd51eb39ef\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:12:13-14:85
103                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
103-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\81abd97b94442baf319fe3cd51eb39ef\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:13:17-129
104                android:value="com.google.firebase.components.ComponentRegistrar" />
104-->[com.google.firebase:firebase-firestore-ktx:24.10.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\81abd97b94442baf319fe3cd51eb39ef\transformed\firebase-firestore-ktx-24.10.0\AndroidManifest.xml:14:17-82
105            <meta-data
105-->[com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:15:13-17:85
106                android:name="com.google.firebase.components:com.google.firebase.functions.FirebaseFunctionsKtxRegistrar"
106-->[com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:16:17-122
107                android:value="com.google.firebase.components.ComponentRegistrar" />
107-->[com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:17:17-82
108            <meta-data
108-->[com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:18:13-20:85
109                android:name="com.google.firebase.components:com.google.firebase.functions.FunctionsRegistrar"
109-->[com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:19:17-111
110                android:value="com.google.firebase.components.ComponentRegistrar" />
110-->[com.google.firebase:firebase-functions:20.4.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\01679ede6a7046599682267b34c6ce76\transformed\firebase-functions-20.4.0\AndroidManifest.xml:20:17-82
111            <meta-data
111-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a033fe4bdd9913054127b312d95fcfda\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:17:13-19:85
112                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
112-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a033fe4bdd9913054127b312d95fcfda\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:18:17-122
113                android:value="com.google.firebase.components.ComponentRegistrar" />
113-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a033fe4bdd9913054127b312d95fcfda\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:19:17-82
114            <meta-data
114-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a033fe4bdd9913054127b312d95fcfda\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:20:13-22:85
115                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
115-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a033fe4bdd9913054127b312d95fcfda\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:21:17-111
116                android:value="com.google.firebase.components.ComponentRegistrar" />
116-->[com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a033fe4bdd9913054127b312d95fcfda\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:22:17-82
117            <meta-data
117-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
118                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
118-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
120            <meta-data
120-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
121                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
121-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\e040b670b45e5b61ba6df26ed3ece033\transformed\firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
123            <meta-data
123-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
124                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
124-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
126            <meta-data
126-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
127                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
127-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\17b8cfb66f3079d51ae905543d77d363\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
129            <meta-data
129-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
130                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
130-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\a8e5569dec292cf0463cfb4269e36164\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
132            <meta-data
132-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
133                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
133-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
135        </service>
136
137        <activity
137-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
138            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
138-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
139            android:excludeFromRecents="true"
139-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
140            android:exported="true"
140-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
141            android:launchMode="singleTask"
141-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
142            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
142-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
143            <intent-filter>
143-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
144                <action android:name="android.intent.action.VIEW" />
144-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
144-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
145
146                <category android:name="android.intent.category.DEFAULT" />
146-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
146-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
147                <category android:name="android.intent.category.BROWSABLE" />
147-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
147-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
148
149                <data
149-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
150                    android:host="firebase.auth"
150-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
151                    android:path="/"
151-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
152                    android:scheme="genericidp" />
152-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
153            </intent-filter>
154        </activity>
155        <activity
155-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
156            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
156-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
157            android:excludeFromRecents="true"
157-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
158            android:exported="true"
158-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
159            android:launchMode="singleTask"
159-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
160            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
160-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
161            <intent-filter>
161-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
162                <action android:name="android.intent.action.VIEW" />
162-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
162-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
163
164                <category android:name="android.intent.category.DEFAULT" />
164-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
164-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
165                <category android:name="android.intent.category.BROWSABLE" />
165-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
165-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
166
167                <data
167-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
168                    android:host="firebase.auth"
168-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
169                    android:path="/"
169-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
170                    android:scheme="recaptcha" />
170-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9df5b5721439605e6efe309bf116636\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
171            </intent-filter>
172        </activity>
173        <activity
173-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:23:9-27:75
174            android:name="com.android.billingclient.api.ProxyBillingActivity"
174-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:24:13-78
175            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
175-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:25:13-96
176            android:exported="false"
176-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:26:13-37
177            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
177-->[com.android.billingclient:billing:6.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\ecc174de945fa3e2448e2598854d2240\transformed\billing-6.1.0\AndroidManifest.xml:27:13-72
178
179        <property
179-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
180            android:name="android.adservices.AD_SERVICES_CONFIG"
180-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
181            android:resource="@xml/ga_ad_services_config" />
181-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1b9817842883966f0e150888e47de2ce\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
182
183        <provider
183-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
184            android:name="com.google.firebase.provider.FirebaseInitProvider"
184-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
185            android:authorities="com.eligi.menteencalma.firebaseinitprovider"
185-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
186            android:directBootAware="true"
186-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
187            android:exported="false"
187-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
188            android:initOrder="100" />
188-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.10\transforms\5f8f388e6524c1acca06b8fa06c1d1a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
189
190        <receiver
190-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
191            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
191-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
192            android:enabled="true"
192-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
193            android:exported="false" >
193-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
194        </receiver>
195
196        <service
196-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
197            android:name="com.google.android.gms.measurement.AppMeasurementService"
197-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
198            android:enabled="true"
198-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
199            android:exported="false" />
199-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
200        <service
200-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
201            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
201-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
202            android:enabled="true"
202-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
203            android:exported="false"
203-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
204            android:permission="android.permission.BIND_JOB_SERVICE" />
204-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\72ea0444c9fc923524efc7fd5981e0bc\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
205
206        <provider
206-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
207            android:name="androidx.startup.InitializationProvider"
207-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
208            android:authorities="com.eligi.menteencalma.androidx-startup"
208-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
209            android:exported="false" >
209-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
210            <meta-data
210-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
211                android:name="androidx.emoji2.text.EmojiCompatInitializer"
211-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
212                android:value="androidx.startup" />
212-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b62f02612c77069e06191771a7102a93\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
213            <meta-data
213-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
214                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
214-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
215                android:value="androidx.startup" />
215-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\8b4bf74aec45b7dcbf3cafe26e4ba90f\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
216            <meta-data
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
217                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
218                android:value="androidx.startup" />
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
219        </provider>
220
221        <uses-library
221-->[androidx.window:window:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d1e6c81f1cfd7a804691a337a31f508c\transformed\window-1.1.0\AndroidManifest.xml:23:9-25:40
222            android:name="androidx.window.extensions"
222-->[androidx.window:window:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d1e6c81f1cfd7a804691a337a31f508c\transformed\window-1.1.0\AndroidManifest.xml:24:13-54
223            android:required="false" />
223-->[androidx.window:window:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d1e6c81f1cfd7a804691a337a31f508c\transformed\window-1.1.0\AndroidManifest.xml:25:13-37
224        <uses-library
224-->[androidx.window:window:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d1e6c81f1cfd7a804691a337a31f508c\transformed\window-1.1.0\AndroidManifest.xml:26:9-28:40
225            android:name="androidx.window.sidecar"
225-->[androidx.window:window:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d1e6c81f1cfd7a804691a337a31f508c\transformed\window-1.1.0\AndroidManifest.xml:27:13-51
226            android:required="false" />
226-->[androidx.window:window:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\d1e6c81f1cfd7a804691a337a31f508c\transformed\window-1.1.0\AndroidManifest.xml:28:13-37
227        <uses-library
227-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\b708d2888d66a53435864c548ee88024\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
228            android:name="android.ext.adservices"
228-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\b708d2888d66a53435864c548ee88024\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
229            android:required="false" />
229-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.10\transforms\b708d2888d66a53435864c548ee88024\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
230
231        <activity
231-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
232            android:name="com.google.android.gms.common.api.GoogleApiActivity"
232-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
233            android:exported="false"
233-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
234            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
234-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bc87254a1d5a4bb9d2cd1a5740dba329\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
235
236        <meta-data
236-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a2f82cac04b16c6074a060829451b01\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
237            android:name="com.google.android.gms.version"
237-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a2f82cac04b16c6074a060829451b01\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
238            android:value="@integer/google_play_services_version" />
238-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\0a2f82cac04b16c6074a060829451b01\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
239
240        <receiver
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
241            android:name="androidx.profileinstaller.ProfileInstallReceiver"
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
242            android:directBootAware="false"
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
243            android:enabled="true"
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
244            android:exported="true"
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
245            android:permission="android.permission.DUMP" >
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
246            <intent-filter>
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
247                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
248            </intent-filter>
249            <intent-filter>
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
250                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
251            </intent-filter>
252            <intent-filter>
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
253                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
254            </intent-filter>
255            <intent-filter>
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
256                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\b45ea97132fd12259cdcbc310ec9ab13\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
257            </intent-filter>
258        </receiver>
259
260        <service
260-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
261            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
261-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
262            android:exported="false" >
262-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
263            <meta-data
263-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
264                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
264-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
265                android:value="cct" />
265-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\59cfff82b4bd065c6d1907967d9b87b2\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
266        </service>
267        <service
267-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
268            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
268-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
269            android:exported="false"
269-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
270            android:permission="android.permission.BIND_JOB_SERVICE" >
270-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
271        </service>
272
273        <receiver
273-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
274            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
274-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
275            android:exported="false" />
275-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10\transforms\def69d27321d4ef0b501efaaae2167cf\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
276    </application>
277
278</manifest>
