// Tipos y constantes para la funcionalidad de Focus Garden y gamificación

// Configuración de niveles de Focus
export const FOCUS_TIERS = [
  { id: 'hierba', name: '<PERSON><PERSON><PERSON>', duration: 5 * 60, icon: '🌾', reward: 2, unlockCost: 0 },
  { id: 'brote', name: '<PERSON>rote', duration: 15 * 60, icon: '🌱', reward: 5, unlockCost: 0 },
  { id: 'arbol', name: '<PERSON>rb<PERSON>', duration: 25 * 60, icon: '🌳', reward: 10, unlockCost: 0 },
  { id: 'cactus', name: '<PERSON><PERSON><PERSON>', duration: 20 * 60, icon: '🌵', reward: 8, unlockCost: 50 },
  { id: 'girasol', name: '<PERSON><PERSON><PERSON>', duration: 30 * 60, icon: '🌻', reward: 15, unlockCost: 150 },
  { id: 'palmera', name: '<PERSON><PERSON>', duration: 40 * 60, icon: '🌴', reward: 20, unlockCost: 250 },
  { id: 'bosque', name: '<PERSON><PERSON>', duration: 45 * 60, icon: '🌲', reward: 25, unlockCost: 350 },
  { id: 'secuoya', name: '<PERSON><PERSON><PERSON>', duration: 60 * 60, icon: '🏞️', reward: 50, unlockCost: 500 },
];

// Lista de logros/achievements
export const ACHIEVEMENTS_LIST = [
  { id: 'FIRST_STEPS', title: 'Primeros Pasos', description: 'Crea tu perfil y comienza tu viaje.', icon: '👋', reward: 10, xp: 50, category: 'social' },
  { id: 'CHAT_STARTER', title: 'Conversador', description: 'Envía tu primer mensaje a la IA.', icon: '💬', reward: 5, xp: 25, category: 'engagement' },
  { id: 'FOCUS_ROOKIE', title: 'Principiante del Focus', description: 'Completa tu primera sesión de focus.', icon: '🌱', reward: 15, xp: 75, category: 'productivity' },
  { id: 'STREAK_7_DAYS', title: 'Una Semana Fuerte', description: 'Mantén una racha de 7 días consecutivos.', icon: '🔥', reward: 50, xp: 200, category: 'wellness' },
  { id: 'FOCUS_MASTER', title: 'Maestro del Focus', description: 'Completa 50 sesiones de focus.', icon: '🧘‍♀️', reward: 100, xp: 500, category: 'productivity' },
  { id: 'OPEN_HEART', title: 'Corazón Abierto', description: 'Completa tu primer registro de estado de ánimo.', icon: '❤️', reward: 20, xp: 100, category: 'wellness' },
  { id: 'ARTICLE_READER', title: 'Lector Ávido', description: 'Lee tu primer artículo generado.', icon: '📖', reward: 10, xp: 50, category: 'engagement' },
  { id: 'CONSTANT_CHATTER', title: 'Chateador Constante', description: 'Envía más de 100 mensajes a la IA.', icon: '⭐', reward: 150, xp: 300, category: 'engagement' },
  { id: 'WELLNESS_MASTER', title: 'Maestro del Bienestar', description: 'Desbloquea todos los demás logros.', icon: '🏆', reward: 500, xp: 1000, category: 'wellness' },
];

// Restricciones para usuarios free
export const FREE_USER_LIMITS = {
  dailyChatMessages: 5,
  dailyRecommendations: 3,
  monthlyArticles: 3, // Corregido: 3 artículos al mes para usuarios free
  maxSavedArticles: 5,
};

export const PREMIUM_USER_LIMITS = {
  dailyChatMessages: -1, // Ilimitado
  dailyRecommendations: -1, // Ilimitado
  monthlyArticles: -1, // Ilimitado
  maxSavedArticles: -1, // Ilimitado
};

// Funciones de gamificación
export const getLevel = (xp) => {
  return Math.floor(Math.sqrt(xp / 100)) + 1;
};

export const getXPForNextLevel = (level) => {
  return Math.pow(level, 2) * 100;
};

export const getXPProgress = (currentXP) => {
  const level = getLevel(currentXP);
  const xpForNext = getXPForNextLevel(level);
  const progress = (currentXP / xpForNext) * 100;
  
  return { level, progress, xpForNext };
};

// Funciones auxiliares para gamificación
export const calculateStreakDays = (focusHistory) => {
  if (!focusHistory || focusHistory.length === 0) return 0;
  
  const today = new Date();
  let streak = 0;
  
  for (let i = 0; i < 30; i++) { // Revisamos los últimos 30 días
    const checkDate = new Date(today);
    checkDate.setDate(today.getDate() - i);
    const dateStr = checkDate.toISOString().split('T')[0];
    
    const hasSessionOnDate = focusHistory.some(session => 
      session.date.startsWith(dateStr) && session.completed
    );
    
    if (hasSessionOnDate) {
      streak++;
    } else if (i === 0) {
      // Si no hay sesión hoy, la racha se rompe
      break;
    } else {
      // Si no hay sesión en un día pasado, pero sí la hay en días más recientes, continuamos
      continue;
    }
  }
  
  return streak;
};

export const checkUnlockedAchievements = (userProfile, userStats) => {
  const unlocked = [];
  
  // Verificar cada logro
  ACHIEVEMENTS_LIST.forEach(achievement => {
    if (userProfile.unlockedAchievements?.includes(achievement.id)) {
      return; // Ya desbloqueado
    }
    
    let shouldUnlock = false;
    
    switch (achievement.id) {
      case 'FIRST_STEPS':
        shouldUnlock = true; // Se desbloquea al crear el perfil
        break;
      case 'CHAT_STARTER':
        shouldUnlock = userStats.totalChatMessages > 0;
        break;
      case 'FOCUS_ROOKIE':
        shouldUnlock = userStats.completedFocusSessions > 0;
        break;
      case 'STREAK_7_DAYS':
        shouldUnlock = calculateStreakDays(userProfile.focusSessionsHistory) >= 7;
        break;
      case 'FOCUS_MASTER':
        shouldUnlock = userStats.completedFocusSessions >= 50;
        break;
      case 'OPEN_HEART':
        shouldUnlock = userStats.moodEntries > 0;
        break;      case 'ARTICLE_READER':
        shouldUnlock = userStats.articlesRead > 0;
        break;
      case 'CONSTANT_CHATTER':
        shouldUnlock = userStats.totalChatMessages >= 100;
        break;
      case 'WELLNESS_MASTER': {
        const otherAchievements = ACHIEVEMENTS_LIST.filter(a => a.id !== 'WELLNESS_MASTER');
        const unlockedCount = otherAchievements.filter(a => 
          userProfile.unlockedAchievements?.includes(a.id)
        ).length;
        shouldUnlock = unlockedCount === otherAchievements.length;
        break;
      }
    }
    
    if (shouldUnlock) {
      unlocked.push(achievement.id);
    }
  });
  
  return unlocked;
};

// Funciones de validación de límites para usuarios free
export const checkUserLimits = (userProfile, subscriptionActive) => {
  if (subscriptionActive) {
    return {
      canSendMessage: true,
      canGenerateRecommendation: true,
      canGenerateArticle: true,
      canSaveArticle: true,
      remainingMessages: -1, // Ilimitado
      remainingRecommendations: -1, // Ilimitado
      remainingArticles: -1, // Ilimitado
      remainingSavedArticles: -1, // Ilimitado
    };
  }

  const today = new Date().toISOString().split('T')[0];
  const currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM

  // Contar uso diario/mensual
  const todayMessages = userProfile?.chatHistory?.filter(msg => 
    msg.timestamp?.startsWith(today)
  )?.length || 0;

  const todayRecommendations = userProfile?.recommendationsHistory?.filter(rec => 
    rec.date?.startsWith(today)
  )?.length || 0;

  const monthlyArticles = userProfile?.articlesHistory?.filter(article => 
    article.date?.startsWith(currentMonth)
  )?.length || 0;

  const savedArticles = userProfile?.savedArticles?.length || 0;

  return {
    canSendMessage: todayMessages < FREE_USER_LIMITS.dailyChatMessages,
    canGenerateRecommendation: todayRecommendations < FREE_USER_LIMITS.dailyRecommendations,
    canGenerateArticle: monthlyArticles < FREE_USER_LIMITS.monthlyArticles,
    canSaveArticle: savedArticles < FREE_USER_LIMITS.maxSavedArticles,
    remainingMessages: Math.max(0, FREE_USER_LIMITS.dailyChatMessages - todayMessages),
    remainingRecommendations: Math.max(0, FREE_USER_LIMITS.dailyRecommendations - todayRecommendations),
    remainingArticles: Math.max(0, FREE_USER_LIMITS.monthlyArticles - monthlyArticles),
    remainingSavedArticles: Math.max(0, FREE_USER_LIMITS.maxSavedArticles - savedArticles),
  };
};

export const getLimitMessage = (limitType, subscriptionActive) => {
  if (subscriptionActive) return null;
  
  const messages = {
    chat: `Has alcanzado el límite de ${FREE_USER_LIMITS.dailyChatMessages} mensajes diarios. Actualiza a Premium para chat ilimitado.`,
    recommendations: `Has alcanzado el límite de ${FREE_USER_LIMITS.dailyRecommendations} recomendaciones diarias. Actualiza a Premium.`,
    articles: `Has alcanzado el límite de ${FREE_USER_LIMITS.monthlyArticles} artículos mensuales. Actualiza a Premium.`,
    savedArticles: `Has alcanzado el límite de ${FREE_USER_LIMITS.maxSavedArticles} artículos guardados. Actualiza a Premium.`,
  };
  
  return messages[limitType] || 'Límite alcanzado. Actualiza a Premium para acceso ilimitado.';
};
