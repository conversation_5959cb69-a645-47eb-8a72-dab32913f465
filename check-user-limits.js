require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

console.log('🔑 Environment check:', {
  hasUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
  hasAnonKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY
});

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function checkUserProfile() {
  const uid = '302982be-c2c6-48bb-8dee-28b7f79fb893';

  console.log('🔍 Checking user profile for UID:', uid);

  // Obtener perfil del usuario
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', uid)
    .single();

  if (profileError) {
    console.log('❌ User not found, creating test profile...');

    // Crear perfil de prueba
    const { data: newProfile, error: createError } = await supabase
      .from('profiles')
      .insert({
        id: uid,
        full_name: 'Test User',
        subscription_status: 'free',
        daily_chat_count: 0,
        last_chat_date: null
      })
      .select()
      .single();

    if (createError) {
      console.error('❌ Error creating test profile:', createError);
      return;
    }

    console.log('✅ Test profile created');
    profile = newProfile;
  }
  
  console.log('👤 User Profile:', {
    id: profile.id,
    fullName: profile.full_name,
    subscriptionStatus: profile.subscription_status,
    dailyChatCount: profile.daily_chat_count,
    lastChatDate: profile.last_chat_date,
    createdAt: profile.created_at
  });
  
  // Verificar uso de artículos del mes actual
  const now = new Date();
  const currentMonth = now.getFullYear() + '-' + (now.getMonth() + 1).toString().padStart(2, '0');
  
  const { data: usage, error: usageError } = await supabase
    .from('user_usage')
    .select('*')
    .eq('user_id', uid)
    .eq('usage_type', 'articles')
    .eq('month', currentMonth);
    
  console.log('📊 Article Usage for', currentMonth, ':', usage);
  
  // Verificar límites actuales
  const isPremium = profile.subscription_status === 'premium';
  const dailyChatLimit = isPremium ? 'Unlimited' : 5;
  const monthlyArticleLimit = isPremium ? 'Unlimited' : 5;
  
  console.log('🎯 Current Limits:', {
    isPremium,
    dailyChatLimit,
    monthlyArticleLimit,
    currentDailyChatCount: profile.daily_chat_count || 0,
    currentMonthlyArticles: usage && usage[0] ? usage[0].count : 0
  });
  
  console.log('\n🧪 Testing Limits Logic:');
  
  // Simular verificación de límites como lo hace la app
  const today = new Date().toISOString().split('T')[0];
  const lastChatDate = profile.last_chat_date;
  const lastChatDay = lastChatDate ? lastChatDate.split('T')[0] : null;
  const isNewDay = !lastChatDay || lastChatDay !== today;
  
  let effectiveDailyChatCount = profile.daily_chat_count || 0;
  if (isNewDay) {
    effectiveDailyChatCount = 0;
    console.log('🔄 New day detected, chat count would reset to 0');
  }
  
  const canSendMessage = isPremium || effectiveDailyChatCount < 5;
  const canGenerateArticle = isPremium || (usage && usage[0] ? usage[0].count : 0) < 5;
  
  console.log('✅ Permissions:', {
    canSendMessage,
    canGenerateArticle,
    remainingMessages: isPremium ? 'Unlimited' : Math.max(0, 5 - effectiveDailyChatCount),
    remainingArticles: isPremium ? 'Unlimited' : Math.max(0, 5 - (usage && usage[0] ? usage[0].count : 0))
  });
}

checkUserProfile().catch(console.error);
