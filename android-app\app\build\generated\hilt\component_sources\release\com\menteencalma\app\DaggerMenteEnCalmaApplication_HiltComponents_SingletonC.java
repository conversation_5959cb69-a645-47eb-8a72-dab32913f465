package com.menteencalma.app;

import android.app.Activity;
import android.app.Service;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.functions.FirebaseFunctions;
import com.menteencalma.app.data.migration.DatabaseMigrationService;
import com.menteencalma.app.data.monitoring.DatabaseMonitoringService;
import com.menteencalma.app.data.service.CloudFunctionsService;
import com.menteencalma.app.data.service.MockCloudFunctionsService;
import com.menteencalma.app.di.AppModule;
import com.menteencalma.app.di.AppModule_ProvideAuthRepositoryFactory;
import com.menteencalma.app.di.AppModule_ProvideCloudFunctionsServiceFactory;
import com.menteencalma.app.di.AppModule_ProvideDatabaseMigrationServiceFactory;
import com.menteencalma.app.di.AppModule_ProvideDatabaseMonitoringServiceFactory;
import com.menteencalma.app.di.AppModule_ProvideDatabaseRepositoryFactory;
import com.menteencalma.app.di.AppModule_ProvideFirebaseAuthFactory;
import com.menteencalma.app.di.AppModule_ProvideFirebaseDatabaseRepositoryFactory;
import com.menteencalma.app.di.AppModule_ProvideFirebaseFirestoreFactory;
import com.menteencalma.app.di.AppModule_ProvideFirebaseFunctionsFactory;
import com.menteencalma.app.di.AppModule_ProvideMonitoredDatabaseRepositoryFactory;
import com.menteencalma.app.di.AppModule_ProvideUserRepositoryFactory;
import com.menteencalma.app.domain.repository.AuthRepository;
import com.menteencalma.app.domain.repository.DatabaseRepository;
import com.menteencalma.app.domain.repository.UserRepository;
import com.menteencalma.app.presentation.screens.articles.ArticlesViewModel;
import com.menteencalma.app.presentation.screens.articles.ArticlesViewModel_HiltModules_KeyModule_ProvideFactory;
import com.menteencalma.app.presentation.screens.chat.ChatViewModel;
import com.menteencalma.app.presentation.screens.chat.ChatViewModel_HiltModules_KeyModule_ProvideFactory;
import com.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel;
import com.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel_HiltModules_KeyModule_ProvideFactory;
import com.menteencalma.app.presentation.viewmodels.AuthViewModel;
import com.menteencalma.app.presentation.viewmodels.AuthViewModel_HiltModules_KeyModule_ProvideFactory;
import com.menteencalma.app.presentation.viewmodels.CreateProfileViewModel;
import com.menteencalma.app.presentation.viewmodels.CreateProfileViewModel_HiltModules_KeyModule_ProvideFactory;
import com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel;
import com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel_HiltModules_KeyModule_ProvideFactory;
import com.menteencalma.app.presentation.viewmodels.LoginViewModel;
import com.menteencalma.app.presentation.viewmodels.LoginViewModel_HiltModules_KeyModule_ProvideFactory;
import com.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel;
import com.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel_HiltModules_KeyModule_ProvideFactory;
import com.menteencalma.app.presentation.viewmodels.ProfileViewModel;
import com.menteencalma.app.presentation.viewmodels.ProfileViewModel_HiltModules_KeyModule_ProvideFactory;
import com.menteencalma.app.presentation.viewmodels.SubscribeViewModel;
import com.menteencalma.app.presentation.viewmodels.SubscribeViewModel_HiltModules_KeyModule_ProvideFactory;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.flags.HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.Preconditions;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaggerMenteEnCalmaApplication_HiltComponents_SingletonC {
  private DaggerMenteEnCalmaApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static MenteEnCalmaApplication_HiltComponents.SingletonC create() {
    return new Builder().build();
  }

  public static final class Builder {
    private Builder() {
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder appModule(AppModule appModule) {
      Preconditions.checkNotNull(appModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule(
        HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule) {
      Preconditions.checkNotNull(hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule);
      return this;
    }

    public MenteEnCalmaApplication_HiltComponents.SingletonC build() {
      return new SingletonCImpl();
    }
  }

  private static final class ActivityRetainedCBuilder implements MenteEnCalmaApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public MenteEnCalmaApplication_HiltComponents.ActivityRetainedC build() {
      return new ActivityRetainedCImpl(singletonCImpl);
    }
  }

  private static final class ActivityCBuilder implements MenteEnCalmaApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public MenteEnCalmaApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements MenteEnCalmaApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public MenteEnCalmaApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements MenteEnCalmaApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public MenteEnCalmaApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements MenteEnCalmaApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public MenteEnCalmaApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements MenteEnCalmaApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public MenteEnCalmaApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements MenteEnCalmaApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public MenteEnCalmaApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends MenteEnCalmaApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends MenteEnCalmaApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends MenteEnCalmaApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends MenteEnCalmaApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Set<String> getViewModelKeys() {
      return ImmutableSet.<String>of(ArticlesViewModel_HiltModules_KeyModule_ProvideFactory.provide(), AuthViewModel_HiltModules_KeyModule_ProvideFactory.provide(), ChatViewModel_HiltModules_KeyModule_ProvideFactory.provide(), CreateProfileViewModel_HiltModules_KeyModule_ProvideFactory.provide(), DatabaseMonitoringViewModel_HiltModules_KeyModule_ProvideFactory.provide(), LoginViewModel_HiltModules_KeyModule_ProvideFactory.provide(), MoodTrackerViewModel_HiltModules_KeyModule_ProvideFactory.provide(), ProfileViewModel_HiltModules_KeyModule_ProvideFactory.provide(), RecommendationsViewModel_HiltModules_KeyModule_ProvideFactory.provide(), SubscribeViewModel_HiltModules_KeyModule_ProvideFactory.provide());
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }
  }

  private static final class ViewModelCImpl extends MenteEnCalmaApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<ArticlesViewModel> articlesViewModelProvider;

    private Provider<AuthViewModel> authViewModelProvider;

    private Provider<ChatViewModel> chatViewModelProvider;

    private Provider<CreateProfileViewModel> createProfileViewModelProvider;

    private Provider<DatabaseMonitoringViewModel> databaseMonitoringViewModelProvider;

    private Provider<LoginViewModel> loginViewModelProvider;

    private Provider<MoodTrackerViewModel> moodTrackerViewModelProvider;

    private Provider<ProfileViewModel> profileViewModelProvider;

    private Provider<RecommendationsViewModel> recommendationsViewModelProvider;

    private Provider<SubscribeViewModel> subscribeViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.articlesViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.authViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.chatViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.createProfileViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.databaseMonitoringViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.loginViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
      this.moodTrackerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 6);
      this.profileViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 7);
      this.recommendationsViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 8);
      this.subscribeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 9);
    }

    @Override
    public Map<String, Provider<ViewModel>> getHiltViewModelMap() {
      return ImmutableMap.<String, Provider<ViewModel>>builderWithExpectedSize(10).put("com.menteencalma.app.presentation.screens.articles.ArticlesViewModel", ((Provider) articlesViewModelProvider)).put("com.menteencalma.app.presentation.viewmodels.AuthViewModel", ((Provider) authViewModelProvider)).put("com.menteencalma.app.presentation.screens.chat.ChatViewModel", ((Provider) chatViewModelProvider)).put("com.menteencalma.app.presentation.viewmodels.CreateProfileViewModel", ((Provider) createProfileViewModelProvider)).put("com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel", ((Provider) databaseMonitoringViewModelProvider)).put("com.menteencalma.app.presentation.viewmodels.LoginViewModel", ((Provider) loginViewModelProvider)).put("com.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel", ((Provider) moodTrackerViewModelProvider)).put("com.menteencalma.app.presentation.viewmodels.ProfileViewModel", ((Provider) profileViewModelProvider)).put("com.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel", ((Provider) recommendationsViewModelProvider)).put("com.menteencalma.app.presentation.viewmodels.SubscribeViewModel", ((Provider) subscribeViewModelProvider)).build();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.menteencalma.app.presentation.screens.articles.ArticlesViewModel 
          return (T) new ArticlesViewModel(singletonCImpl.provideCloudFunctionsServiceProvider.get(), singletonCImpl.provideDatabaseRepositoryProvider.get(), singletonCImpl.provideAuthRepositoryProvider.get(), singletonCImpl.provideFirebaseFirestoreProvider.get());

          case 1: // com.menteencalma.app.presentation.viewmodels.AuthViewModel 
          return (T) new AuthViewModel(singletonCImpl.provideAuthRepositoryProvider.get(), singletonCImpl.provideUserRepositoryProvider.get());

          case 2: // com.menteencalma.app.presentation.screens.chat.ChatViewModel 
          return (T) new ChatViewModel(singletonCImpl.provideCloudFunctionsServiceProvider.get(), singletonCImpl.provideDatabaseRepositoryProvider.get(), singletonCImpl.provideAuthRepositoryProvider.get());

          case 3: // com.menteencalma.app.presentation.viewmodels.CreateProfileViewModel 
          return (T) new CreateProfileViewModel(singletonCImpl.provideAuthRepositoryProvider.get(), singletonCImpl.provideUserRepositoryProvider.get(), singletonCImpl.provideFirebaseFunctionsProvider.get());

          case 4: // com.menteencalma.app.presentation.viewmodels.DatabaseMonitoringViewModel 
          return (T) new DatabaseMonitoringViewModel(singletonCImpl.provideDatabaseMonitoringServiceProvider.get(), singletonCImpl.provideDatabaseMigrationServiceProvider.get());

          case 5: // com.menteencalma.app.presentation.viewmodels.LoginViewModel 
          return (T) new LoginViewModel(singletonCImpl.provideAuthRepositoryProvider.get(), singletonCImpl.provideUserRepositoryProvider.get());

          case 6: // com.menteencalma.app.presentation.viewmodels.MoodTrackerViewModel 
          return (T) new MoodTrackerViewModel(singletonCImpl.provideDatabaseRepositoryProvider.get(), singletonCImpl.provideAuthRepositoryProvider.get(), singletonCImpl.provideFirebaseFirestoreProvider.get());

          case 7: // com.menteencalma.app.presentation.viewmodels.ProfileViewModel 
          return (T) new ProfileViewModel(singletonCImpl.provideDatabaseRepositoryProvider.get(), singletonCImpl.provideAuthRepositoryProvider.get());

          case 8: // com.menteencalma.app.presentation.screens.recommendations.RecommendationsViewModel 
          return (T) new RecommendationsViewModel(singletonCImpl.provideCloudFunctionsServiceProvider.get(), singletonCImpl.provideDatabaseRepositoryProvider.get(), singletonCImpl.provideAuthRepositoryProvider.get());

          case 9: // com.menteencalma.app.presentation.viewmodels.SubscribeViewModel 
          return (T) new SubscribeViewModel(singletonCImpl.provideDatabaseRepositoryProvider.get(), singletonCImpl.provideAuthRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends MenteEnCalmaApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;

      initialize();

    }

    @SuppressWarnings("unchecked")
    private void initialize() {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends MenteEnCalmaApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }
  }

  private static final class SingletonCImpl extends MenteEnCalmaApplication_HiltComponents.SingletonC {
    private final SingletonCImpl singletonCImpl = this;

    private Provider<FirebaseFunctions> provideFirebaseFunctionsProvider;

    private Provider<MockCloudFunctionsService> mockCloudFunctionsServiceProvider;

    private Provider<CloudFunctionsService> provideCloudFunctionsServiceProvider;

    private Provider<FirebaseFirestore> provideFirebaseFirestoreProvider;

    private Provider<DatabaseRepository> provideFirebaseDatabaseRepositoryProvider;

    private Provider<DatabaseMonitoringService> provideDatabaseMonitoringServiceProvider;

    private Provider<DatabaseRepository> provideMonitoredDatabaseRepositoryProvider;

    private Provider<DatabaseRepository> provideDatabaseRepositoryProvider;

    private Provider<FirebaseAuth> provideFirebaseAuthProvider;

    private Provider<AuthRepository> provideAuthRepositoryProvider;

    private Provider<UserRepository> provideUserRepositoryProvider;

    private Provider<DatabaseMigrationService> provideDatabaseMigrationServiceProvider;

    private SingletonCImpl() {

      initialize();

    }

    @SuppressWarnings("unchecked")
    private void initialize() {
      this.provideFirebaseFunctionsProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseFunctions>(singletonCImpl, 1));
      this.mockCloudFunctionsServiceProvider = DoubleCheck.provider(new SwitchingProvider<MockCloudFunctionsService>(singletonCImpl, 2));
      this.provideCloudFunctionsServiceProvider = DoubleCheck.provider(new SwitchingProvider<CloudFunctionsService>(singletonCImpl, 0));
      this.provideFirebaseFirestoreProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseFirestore>(singletonCImpl, 6));
      this.provideFirebaseDatabaseRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<DatabaseRepository>(singletonCImpl, 5));
      this.provideDatabaseMonitoringServiceProvider = DoubleCheck.provider(new SwitchingProvider<DatabaseMonitoringService>(singletonCImpl, 7));
      this.provideMonitoredDatabaseRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<DatabaseRepository>(singletonCImpl, 4));
      this.provideDatabaseRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<DatabaseRepository>(singletonCImpl, 3));
      this.provideFirebaseAuthProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseAuth>(singletonCImpl, 9));
      this.provideAuthRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<AuthRepository>(singletonCImpl, 8));
      this.provideUserRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<UserRepository>(singletonCImpl, 10));
      this.provideDatabaseMigrationServiceProvider = DoubleCheck.provider(new SwitchingProvider<DatabaseMigrationService>(singletonCImpl, 11));
    }

    @Override
    public void injectMenteEnCalmaApplication(MenteEnCalmaApplication arg0) {
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return ImmutableSet.<Boolean>of();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.menteencalma.app.data.service.CloudFunctionsService 
          return (T) AppModule_ProvideCloudFunctionsServiceFactory.provideCloudFunctionsService(singletonCImpl.provideFirebaseFunctionsProvider.get(), singletonCImpl.mockCloudFunctionsServiceProvider.get());

          case 1: // com.google.firebase.functions.FirebaseFunctions 
          return (T) AppModule_ProvideFirebaseFunctionsFactory.provideFirebaseFunctions();

          case 2: // com.menteencalma.app.data.service.MockCloudFunctionsService 
          return (T) new MockCloudFunctionsService();

          case 3: // com.menteencalma.app.domain.repository.DatabaseRepository 
          return (T) AppModule_ProvideDatabaseRepositoryFactory.provideDatabaseRepository(singletonCImpl.provideMonitoredDatabaseRepositoryProvider.get());

          case 4: // @com.menteencalma.app.di.MonitoredDatabase com.menteencalma.app.domain.repository.DatabaseRepository 
          return (T) AppModule_ProvideMonitoredDatabaseRepositoryFactory.provideMonitoredDatabaseRepository(singletonCImpl.provideFirebaseDatabaseRepositoryProvider.get(), singletonCImpl.provideDatabaseMonitoringServiceProvider.get());

          case 5: // @com.menteencalma.app.di.FirebaseDatabase com.menteencalma.app.domain.repository.DatabaseRepository 
          return (T) AppModule_ProvideFirebaseDatabaseRepositoryFactory.provideFirebaseDatabaseRepository(singletonCImpl.provideFirebaseFirestoreProvider.get());

          case 6: // com.google.firebase.firestore.FirebaseFirestore 
          return (T) AppModule_ProvideFirebaseFirestoreFactory.provideFirebaseFirestore();

          case 7: // com.menteencalma.app.data.monitoring.DatabaseMonitoringService 
          return (T) AppModule_ProvideDatabaseMonitoringServiceFactory.provideDatabaseMonitoringService();

          case 8: // com.menteencalma.app.domain.repository.AuthRepository 
          return (T) AppModule_ProvideAuthRepositoryFactory.provideAuthRepository(singletonCImpl.provideFirebaseAuthProvider.get());

          case 9: // com.google.firebase.auth.FirebaseAuth 
          return (T) AppModule_ProvideFirebaseAuthFactory.provideFirebaseAuth();

          case 10: // com.menteencalma.app.domain.repository.UserRepository 
          return (T) AppModule_ProvideUserRepositoryFactory.provideUserRepository(singletonCImpl.provideFirebaseFirestoreProvider.get());

          case 11: // com.menteencalma.app.data.migration.DatabaseMigrationService 
          return (T) AppModule_ProvideDatabaseMigrationServiceFactory.provideDatabaseMigrationService(singletonCImpl.provideDatabaseRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
