package com.menteencalma.app.presentation.viewmodels;

import com.menteencalma.app.domain.repository.AuthRepository;
import com.menteencalma.app.domain.repository.DatabaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProfileViewModel_Factory implements Factory<ProfileViewModel> {
  private final Provider<DatabaseRepository> databaseRepositoryProvider;

  private final Provider<AuthRepository> authRepositoryProvider;

  public ProfileViewModel_Factory(Provider<DatabaseRepository> databaseRepositoryProvider,
      Provider<AuthRepository> authRepositoryProvider) {
    this.databaseRepositoryProvider = databaseRepositoryProvider;
    this.authRepositoryProvider = authRepositoryProvider;
  }

  @Override
  public ProfileViewModel get() {
    return newInstance(databaseRepositoryProvider.get(), authRepositoryProvider.get());
  }

  public static ProfileViewModel_Factory create(
      Provider<DatabaseRepository> databaseRepositoryProvider,
      Provider<AuthRepository> authRepositoryProvider) {
    return new ProfileViewModel_Factory(databaseRepositoryProvider, authRepositoryProvider);
  }

  public static ProfileViewModel newInstance(DatabaseRepository databaseRepository,
      AuthRepository authRepository) {
    return new ProfileViewModel(databaseRepository, authRepository);
  }
}
