'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/contexts/supabase-auth-context';
import { useSupabaseChat } from '@/hooks/use-supabase-chat';
import { useUserPsychologicalAnalysis } from '@/hooks/use-user-psychological-analysis';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Send, Loader2, Brain, Heart, FileText } from 'lucide-react'; 
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'; 
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { format, isToday, isYesterday } from 'date-fns';
import { es } from 'date-fns/locale';

// Componente de Chat separado
interface ChatComponentProps {
  therapistAvatar: string;
  therapistName: string;
  isDoctor: boolean;
  analysis: any;
  getRiskColor: (level: string) => string;
  getRiskText: (level: string) => string;
  scrollAreaRef: React.RefObject<HTMLDivElement>;
  chatLoading: boolean;
  messages: any[];
  formatMessageDate: (date: Date) => string;
  userProfile: any;
  input: string;
  setInput: (value: string) => void;
  handleSendMessage: () => void;
}

const ChatComponent: React.FC<ChatComponentProps> = ({
  therapistAvatar,
  therapistName,
  isDoctor,
  analysis,
  getRiskColor,
  getRiskText,
  scrollAreaRef,
  chatLoading,
  messages,
  formatMessageDate,
  userProfile,
  input,
  setInput,
  handleSendMessage
}) => (
  <Card className="flex-1 flex flex-col bg-white/90 backdrop-blur-sm">
    <CardHeader className="flex flex-row items-center space-y-0 pb-4">
      <Avatar className="w-10 h-10 mr-3">
        <AvatarImage src={therapistAvatar} alt={therapistName} />
        <AvatarFallback>🧠</AvatarFallback>
      </Avatar>
      <div className="flex-1">
        <CardTitle className="text-lg">{therapistName}</CardTitle>
        <p className="text-sm text-muted-foreground">Tu terapeuta virtual con IA</p>
      </div>
      {isDoctor && analysis && (
        <Badge className={getRiskColor(analysis.risk_level)}>
          Riesgo: {getRiskText(analysis.risk_level)}
        </Badge>
      )}
    </CardHeader>

    <CardContent className="flex-1 flex flex-col p-0">
      <ScrollArea className="flex-1 px-6" ref={scrollAreaRef}>
        <div className="space-y-4 py-4">
          {chatLoading && messages.length === 0 ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={`chat-skeleton-${i}`} className="flex gap-3">
                  <Skeleton className="w-8 h-8 rounded-full" />
                  <Skeleton className="h-4 w-[250px]" />
                </div>
              ))}
            </div>
          ) : (
            messages.map((message) => (
              <div key={message.id} className={`flex gap-3 ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                {message.sender === 'ai' && (
                  <Avatar className="w-8 h-8">
                    <AvatarImage src={therapistAvatar} alt={therapistName} />
                    <AvatarFallback>🧠</AvatarFallback>
                  </Avatar>
                )}
                <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.sender === 'user'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  <p className="text-sm">{message.content}</p>
                  <p className={`text-xs mt-1 ${
                    message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'
                  }`}>
                    {formatMessageDate(new Date(message.created_at))}
                  </p>
                </div>
                {message.sender === 'user' && (
                  <Avatar className="w-8 h-8">
                    <AvatarFallback className="bg-blue-500 text-white">
                      {userProfile?.fullName?.charAt(0) ?? 'U'}
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))
          )}
        </div>
      </ScrollArea>

      <div className="p-6 border-t">
        <div className="flex gap-2">
          <Input
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Escribe tu mensaje..."
            onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
            disabled={chatLoading}
          />
          <Button
            onClick={handleSendMessage}
            disabled={chatLoading || !input.trim()}
            size="icon"
          >
            {chatLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
);

export default function ChatPage() {
  const { user, userProfile } = useAuth();
  const { toast } = useToast();
  
  const { 
    messages, 
    isLoading: chatLoading, 
    sendMessage,
  } = useSupabaseChat();
  
  const { 
    analysis, 
    generateNewAnalysis,
    getPatientSummaryForDoctor,
    isLoading: analysisLoading 
  } = useUserPsychologicalAnalysis();

  const [input, setInput] = useState('');
  const [isGeneratingAnalysis, setIsGeneratingAnalysis] = useState(false);
  const [isDoctorSummary, setIsDoctorSummary] = useState(false);
  const [doctorSummary, setDoctorSummary] = useState('');
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const therapistGender = userProfile?.preferredTherapist === 'alejandro' ? 'male' : 'female';
  const therapistAvatar = therapistGender === 'male' 
    ? '/images/avatar-terapeuta-hombre.png' 
    : '/images/avatar-terapeuta-mujer.png';
  const therapistName = therapistGender === 'male' ? 'Psicólogo Alejandro' : 'Psicóloga Aurora';

  // Determinar si el usuario es doctor
  const isDoctor = userProfile?.email?.includes('doctor') ?? false;

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTo({ 
        top: scrollAreaRef.current.scrollHeight, 
        behavior: 'smooth'
      });
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (input.trim() === '' || !user || !userProfile) return;

    try {
      await sendMessage(input);
      setInput('');
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: 'Error',
        description: 'No se pudo enviar el mensaje. Inténtalo de nuevo.',
        variant: 'destructive',
      });
    }
  };

  const handleGenerateAnalysis = async () => {
    setIsGeneratingAnalysis(true);
    try {
      await generateNewAnalysis();
      toast({
        title: 'Análisis Generado',
        description: 'Se ha generado un nuevo análisis psicológico.',
      });
    } catch (error) {
      console.error('Error generating analysis:', error);
      toast({
        title: 'Error',
        description: 'No se pudo generar el análisis.',
        variant: 'destructive',
      });
    } finally {
      setIsGeneratingAnalysis(false);
    }
  };

  const handleGenerateDoctorSummary = async () => {
    setIsDoctorSummary(true);
    try {
      const summary = await getPatientSummaryForDoctor();
      setDoctorSummary(summary);
      toast({
        title: 'Resumen Generado',
        description: 'Se ha generado el resumen médico.',
      });
    } catch (error) {
      console.error('Error generating doctor summary:', error);
      toast({
        title: 'Error',
        description: 'No se pudo generar el resumen médico.',
        variant: 'destructive',
      });
    } finally {
      setIsDoctorSummary(false);
    }
  };

  const formatMessageDate = (date: Date) => {
    if (isToday(date)) {
      return format(date, 'HH:mm', { locale: es });
    } else if (isYesterday(date)) {
      return `Ayer ${format(date, 'HH:mm', { locale: es })}`;
    } else {
      return format(date, 'dd/MM HH:mm', { locale: es });
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRiskText = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'Bajo';
      case 'medium': return 'Moderado';
      case 'high': return 'Alto';
      case 'critical': return 'Crítico';
      default: return 'No evaluado';
    }
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Card className="w-96">
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Inicia sesión para acceder al chat
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }



  // Si es doctor, mostrar todas las pestañas
  if (isDoctor) {
    return (
      <div className="flex flex-col h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <Tabs defaultValue="chat" className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-3 bg-white/80 backdrop-blur-sm m-4 rounded-lg">
            <TabsTrigger value="chat" className="flex items-center gap-2">
              <Heart className="w-4 h-4" />
              Chat
            </TabsTrigger>
            <TabsTrigger value="analysis" className="flex items-center gap-2">
              <Brain className="w-4 h-4" />
              Análisis Psicológico
            </TabsTrigger>
            <TabsTrigger value="medical" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Resumen Médico
            </TabsTrigger>
          </TabsList>

          <TabsContent value="chat" className="flex-1 flex flex-col px-4 pb-4 space-y-0">
            <ChatComponent
              therapistAvatar={therapistAvatar}
              therapistName={therapistName}
              isDoctor={isDoctor}
              analysis={analysis}
              getRiskColor={getRiskColor}
              getRiskText={getRiskText}
              scrollAreaRef={scrollAreaRef}
              chatLoading={chatLoading}
              messages={messages}
              formatMessageDate={formatMessageDate}
              userProfile={userProfile}
              input={input}
              setInput={setInput}
              handleSendMessage={handleSendMessage}
            />
          </TabsContent>

          <TabsContent value="analysis" className="flex-1 flex flex-col px-4 pb-4">
            <Card className="flex-1 bg-white/90 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="w-5 h-5" />
                  Análisis Psicológico
                </CardTitle>
              </CardHeader>
              <CardContent>
                {analysisLoading || isGeneratingAnalysis ? (
                  <div className="space-y-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                ) : analysis ? (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="font-semibold mb-2">Nivel de Riesgo</h3>
                        <Badge className={getRiskColor(analysis.risk_level)}>
                          {getRiskText(analysis.risk_level)}
                        </Badge>
                      </div>
                      <div>
                        <h3 className="font-semibold mb-2">Confianza de IA</h3>
                        <p className="text-sm text-muted-foreground">
                          {Math.round(analysis.ai_confidence_score * 100)}%
                        </p>
                      </div>
                    </div>
                    <div>
                      <h3 className="font-semibold mb-2">Notas de Progreso</h3>
                      <p className="text-sm text-muted-foreground">
                        {analysis.progress_notes || 'Análisis inicial en progreso'}
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="text-center space-y-4">
                    <p className="text-muted-foreground">
                      No hay análisis psicológico disponible aún
                    </p>
                    <Button onClick={handleGenerateAnalysis} disabled={isGeneratingAnalysis}>
                      {isGeneratingAnalysis ? 'Generando...' : 'Generar Análisis'}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="medical" className="flex-1 flex flex-col px-4 pb-4">
            <Card className="flex-1 bg-white/90 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Resumen Médico
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isDoctorSummary ? (
                  <div className="space-y-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                ) : doctorSummary ? (
                  <div className="space-y-4">
                    <pre className="whitespace-pre-wrap text-sm bg-gray-50 p-4 rounded-lg">
                      {doctorSummary}
                    </pre>
                    <Button 
                      onClick={handleGenerateDoctorSummary}
                      variant="outline"
                      className="w-full"
                    >
                      Actualizar Resumen
                    </Button>
                  </div>
                ) : (
                  <div className="text-center space-y-4">
                    <p className="text-muted-foreground">
                      Genera un resumen médico profesional
                    </p>
                    <Button onClick={handleGenerateDoctorSummary} disabled={isDoctorSummary}>
                      {isDoctorSummary ? 'Generando...' : 'Generar Resumen'}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  // Para usuarios normales, solo mostrar el chat
  return (
    <div className="flex flex-col h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <ChatComponent
        therapistAvatar={therapistAvatar}
        therapistName={therapistName}
        isDoctor={isDoctor}
        analysis={analysis}
        getRiskColor={getRiskColor}
        getRiskText={getRiskText}
        scrollAreaRef={scrollAreaRef}
        chatLoading={chatLoading}
        messages={messages}
        formatMessageDate={formatMessageDate}
        userProfile={userProfile}
        input={input}
        setInput={setInput}
        handleSendMessage={handleSendMessage}
      />
    </div>
  );
}
