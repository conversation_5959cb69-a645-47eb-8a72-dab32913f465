/**
 * Componente de selección de emociones mejorado
 * Diseño visual atractivo con animaciones y feedback
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

// Definición mejorada de emociones con más datos visuales
export const emotionOptions = [
  {
    id: 'ecstatic',
    label: 'Extático',
    value: 5,
    emoji: '🤩',
    color: 'from-green-400 to-emerald-500',
    description: 'Súper feliz y energizado',
    keywords: ['eufórico', 'radiante', 'exultante']
  },
  {
    id: 'happy',
    label: 'Feliz',
    value: 4,
    emoji: '😊',
    color: 'from-green-300 to-green-400',
    description: 'Contento y positivo',
    keywords: ['alegre', 'contento', 'optimista']
  },
  {
    id: 'content',
    label: 'Satisfecho',
    value: 4,
    emoji: '😌',
    color: 'from-blue-300 to-blue-400',
    description: 'En paz y satisfecho',
    keywords: ['tranquilo', 'sereno', 'pleno']
  },
  {
    id: 'neutral',
    label: 'Neutral',
    value: 3,
    emoji: '😐',
    color: 'from-gray-300 to-gray-400',
    description: 'Sin emociones fuertes',
    keywords: ['normal', 'estable', 'equilibrado']
  },
  {
    id: 'tired',
    label: 'Cansado',
    value: 2,
    emoji: '😴',
    color: 'from-purple-300 to-purple-400',
    description: 'Sin energía, fatigado',
    keywords: ['agotado', 'somnoliento', 'sin energía']
  },
  {
    id: 'sad',
    label: 'Triste',
    value: 2,
    emoji: '😢',
    color: 'from-blue-400 to-indigo-500',
    description: 'Con pena o melancolía',
    keywords: ['melancólico', 'desanimado', 'abatido']
  },
  {
    id: 'anxious',
    label: 'Ansioso',
    value: 2,
    emoji: '😰',
    color: 'from-yellow-400 to-orange-500',
    description: 'Preocupado e inquieto',
    keywords: ['nervioso', 'inquieto', 'preocupado']
  },
  {
    id: 'stressed',
    label: 'Estresado',
    value: 1,
    emoji: '😫',
    color: 'from-orange-400 to-red-500',
    description: 'Bajo presión y tensión',
    keywords: ['abrumado', 'presionado', 'tenso']
  },
  {
    id: 'angry',
    label: 'Enojado',
    value: 1,
    emoji: '😠',
    color: 'from-red-400 to-red-600',
    description: 'Irritado o molesto',
    keywords: ['furioso', 'irritado', 'molesto']
  },
  {
    id: 'depressed',
    label: 'Deprimido',
    value: 1,
    emoji: '😞',
    color: 'from-gray-500 to-gray-700',
    description: 'Muy bajo de ánimo',
    keywords: ['desalentado', 'desesperanzado', 'vacío']
  }
];

interface EmotionSelectorProps {
  selectedEmotion?: string;
  onEmotionSelect: (emotionId: string) => void;
  description?: string;
  onDescriptionChange: (description: string) => void;
  onSave: () => void;
  isLoading?: boolean;
}

const EmotionCard = ({ 
  emotion, 
  isSelected, 
  onClick 
}: { 
  emotion: typeof emotionOptions[0]; 
  isSelected: boolean; 
  onClick: () => void; 
}) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        "relative group p-4 rounded-2xl border-2 transition-all duration-300 text-left w-full",
        "hover:scale-105 hover:shadow-lg active:scale-95",
        isSelected 
          ? "border-primary bg-primary/10 shadow-md scale-105" 
          : "border-border hover:border-primary/50 bg-card"
      )}
    >
      {/* Gradient background for selected state */}
      {isSelected && (
        <div className={cn(
          "absolute inset-0 opacity-20 rounded-2xl bg-gradient-to-br",
          emotion.color
        )} />
      )}
      
      <div className="relative z-10 flex flex-col items-center gap-3">
        {/* Emoji */}
        <div className={cn(
          "text-4xl transition-transform duration-200",
          isSelected && "scale-110"
        )}>
          {emotion.emoji}
        </div>
        
        {/* Label */}
        <div className="text-center">
          <h3 className={cn(
            "font-semibold transition-colors",
            isSelected ? "text-primary" : "text-foreground"
          )}>
            {emotion.label}
          </h3>
          <p className="text-xs text-muted-foreground mt-1">
            {emotion.description}
          </p>
        </div>
        
        {/* Value indicator */}
        <div className="flex gap-1">
          {[...Array(5)].map((_, i) => (
            <div
              key={`emotion-intensity-${emotion.id}-${i}`}
              className={cn(
                "w-2 h-2 rounded-full transition-all duration-200",
                i < emotion.value 
                  ? (isSelected ? "bg-primary" : "bg-muted-foreground/60")
                  : "bg-muted/50"
              )}
            />
          ))}
        </div>
        
        {/* Keywords */}
        {isSelected && (
          <div className="flex flex-wrap gap-1 justify-center">
            {emotion.keywords.slice(0, 2).map((keyword) => (
              <Badge 
                key={keyword} 
                variant="secondary" 
                className="text-xs px-2 py-0"
              >
                {keyword}
              </Badge>
            ))}
          </div>
        )}
      </div>
    </button>
  );
};

export default function EnhancedEmotionSelector({
  selectedEmotion,
  onEmotionSelect,
  description = '',
  onDescriptionChange,
  onSave,
  isLoading = false
}: EmotionSelectorProps) {
  const [step, setStep] = useState<'emotion' | 'details'>('emotion');
  
  const selectedEmotionData = emotionOptions.find(e => e.id === selectedEmotion);

  const handleContinue = () => {
    if (selectedEmotion) {
      setStep('details');
    }
  };

  const handleBack = () => {
    setStep('emotion');
  };

  const handleSave = () => {
    onSave();
    // Reset to emotion selection after save
    setStep('emotion');
  };

  return (
    <div className="space-y-6">
      {step === 'emotion' ? (
        <>
          {/* Emotion Selection */}
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="flex items-center justify-center gap-2">
                <span className="text-2xl">💭</span>
                ¿Cómo te sientes ahora?
              </CardTitle>
              <CardDescription>
                Selecciona la emoción que mejor describe tu estado actual
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {emotionOptions.map((emotion) => (
                  <EmotionCard
                    key={emotion.id}
                    emotion={emotion}
                    isSelected={selectedEmotion === emotion.id}
                    onClick={() => onEmotionSelect(emotion.id)}
                  />
                ))}
              </div>
              
              {selectedEmotion && (
                <div className="mt-6 flex justify-center">
                  <Button onClick={handleContinue} size="lg">
                    Continuar
                    <span className="ml-2">→</span>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      ) : (
        <>
          {/* Details Step */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-3">
                <Button variant="ghost" size="sm" onClick={handleBack}>
                  ← Volver
                </Button>
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <span className="text-2xl">{selectedEmotionData?.emoji}</span>
                    Te sientes {selectedEmotionData?.label.toLowerCase()}
                  </CardTitle>
                  <CardDescription>
                    Comparte más detalles sobre cómo te sientes (opcional)
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Emotion summary */}
              <div className={cn(
                "p-4 rounded-xl bg-gradient-to-br",
                selectedEmotionData?.color,
                "bg-opacity-20 border"
              )}>
                <div className="flex items-center gap-3">
                  <span className="text-3xl">{selectedEmotionData?.emoji}</span>
                  <div>
                    <h3 className="font-semibold text-lg">
                      {selectedEmotionData?.label}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {selectedEmotionData?.description}
                    </p>
                  </div>
                  <div className="ml-auto">
                    <div className="flex gap-1">
                      {[...Array(5)].map((_, i) => (
                        <div
                          key={`selected-emotion-intensity-${i}`}
                          className={cn(
                            "w-3 h-3 rounded-full",
                            i < (selectedEmotionData?.value ?? 0) 
                              ? "bg-foreground/80" 
                              : "bg-foreground/20"
                          )}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Keywords */}
              <div>
                <h4 className="text-sm font-medium mb-2">Palabras relacionadas:</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedEmotionData?.keywords.map((keyword) => (
                    <Badge key={keyword} variant="outline">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  ¿Qué está pasando? (Opcional)
                </label>
                <Textarea
                  value={description}
                  onChange={(e) => onDescriptionChange(e.target.value)}
                  placeholder="Describe brevemente qué situaciones o pensamientos están influyendo en cómo te sientes..."
                  className="min-h-[100px] resize-none"
                  maxLength={300}
                />
                <div className="text-xs text-muted-foreground text-right">
                  {description.length}/300
                </div>
              </div>

              {/* Action buttons */}
              <div className="flex gap-3">
                <Button
                  onClick={handleSave}
                  disabled={isLoading}
                  className="flex-1"
                  size="lg"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin mr-2 h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                      Guardando...
                    </>
                  ) : (
                    <>
                      💾 Guardar registro
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
