package com.menteencalma.app.presentation.viewmodels;

import com.menteencalma.app.data.migration.DatabaseMigrationService;
import com.menteencalma.app.data.monitoring.DatabaseMonitoringService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseMonitoringViewModel_Factory implements Factory<DatabaseMonitoringViewModel> {
  private final Provider<DatabaseMonitoringService> monitoringServiceProvider;

  private final Provider<DatabaseMigrationService> migrationServiceProvider;

  public DatabaseMonitoringViewModel_Factory(
      Provider<DatabaseMonitoringService> monitoringServiceProvider,
      Provider<DatabaseMigrationService> migrationServiceProvider) {
    this.monitoringServiceProvider = monitoringServiceProvider;
    this.migrationServiceProvider = migrationServiceProvider;
  }

  @Override
  public DatabaseMonitoringViewModel get() {
    return newInstance(monitoringServiceProvider.get(), migrationServiceProvider.get());
  }

  public static DatabaseMonitoringViewModel_Factory create(
      Provider<DatabaseMonitoringService> monitoringServiceProvider,
      Provider<DatabaseMigrationService> migrationServiceProvider) {
    return new DatabaseMonitoringViewModel_Factory(monitoringServiceProvider, migrationServiceProvider);
  }

  public static DatabaseMonitoringViewModel newInstance(DatabaseMonitoringService monitoringService,
      DatabaseMigrationService migrationService) {
    return new DatabaseMonitoringViewModel(monitoringService, migrationService);
  }
}
