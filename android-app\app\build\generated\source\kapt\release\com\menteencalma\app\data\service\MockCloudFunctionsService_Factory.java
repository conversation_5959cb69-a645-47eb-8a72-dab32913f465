package com.menteencalma.app.data.service;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MockCloudFunctionsService_Factory implements Factory<MockCloudFunctionsService> {
  @Override
  public MockCloudFunctionsService get() {
    return newInstance();
  }

  public static MockCloudFunctionsService_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static MockCloudFunctionsService newInstance() {
    return new MockCloudFunctionsService();
  }

  private static final class InstanceHolder {
    private static final MockCloudFunctionsService_Factory INSTANCE = new MockCloudFunctionsService_Factory();
  }
}
