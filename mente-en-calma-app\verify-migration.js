#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 VERIFICANDO MIGRACIÓN DE FIREBASE A SUPABASE...\n');

// Verificar que archivos Firebase hayan sido eliminados
const firebaseFiles = [
  'src/config/firebase.js',
  'src/contexts/AuthContext.js',
  'src/contexts/SubscriptionContext.js'
];

let allFirebaseFilesRemoved = true;

console.log('📂 Verificando eliminación de archivos Firebase:');
firebaseFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ❌ ${file} - TODAVÍA EXISTE`);
    allFirebaseFilesRemoved = false;
  } else {
    console.log(`   ✅ ${file} - ELIMINADO`);
  }
});

// Verificar package.json
console.log('\n📦 Verificando package.json:');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

if (packageJson.dependencies.firebase) {
  console.log('   ❌ Dependencia "firebase" todavía presente');
  allFirebaseFilesRemoved = false;
} else {
  console.log('   ✅ Dependencia "firebase" eliminada');
}

if (packageJson.dependencies['@supabase/supabase-js']) {
  console.log('   ✅ Dependencia "@supabase/supabase-js" presente');
} else {
  console.log('   ❌ Dependencia "@supabase/supabase-js" faltante');
  allFirebaseFilesRemoved = false;
}

// Verificar archivos de configuración
console.log('\n⚙️ Verificando configuración:');

if (fs.existsSync('src/config/supabase.js')) {
  console.log('   ✅ Configuración de Supabase presente');
} else {
  console.log('   ❌ Configuración de Supabase faltante');
  allFirebaseFilesRemoved = false;
}

if (fs.existsSync('.env')) {
  const envContent = fs.readFileSync('.env', 'utf8');
  if (envContent.includes('EXPO_PUBLIC_SUPABASE_URL')) {
    console.log('   ✅ Variables de entorno de Supabase configuradas');
  } else {
    console.log('   ❌ Variables de entorno de Supabase faltantes');
    allFirebaseFilesRemoved = false;
  }
} else {
  console.log('   ❌ Archivo .env faltante');
  allFirebaseFilesRemoved = false;
}

// Verificar contextos de Supabase
console.log('\n🔗 Verificando contextos de Supabase:');
const supabaseContexts = [
  'src/contexts/SupabaseAuthContext.js',
  'src/contexts/SupabaseSubscriptionContext.js',
  'src/contexts/SupabaseChatContext.js',
  'src/contexts/SupabaseMoodContext.js',
  'src/contexts/SupabaseContentContext.js'
];

supabaseContexts.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file} - PRESENTE`);
  } else {
    console.log(`   ❌ ${file} - FALTANTE`);
    allFirebaseFilesRemoved = false;
  }
});

// Verificar screens
console.log('\n📱 Verificando imports en screens:');
const screenFiles = fs.readdirSync('src/screens').filter(file => file.endsWith('.js'));

screenFiles.forEach(file => {
  const filePath = path.join('src/screens', file);
  const content = fs.readFileSync(filePath, 'utf8');
  
  if (content.includes("from '../contexts/AuthContext'") || 
      content.includes("from '../contexts/SubscriptionContext'")) {
    console.log(`   ❌ ${file} - TODAVÍA USA CONTEXTOS DE FIREBASE`);
    allFirebaseFilesRemoved = false;
  } else if (content.includes("SupabaseAuthContext") || 
             content.includes("SupabaseSubscriptionContext")) {
    console.log(`   ✅ ${file} - USA CONTEXTOS DE SUPABASE`);
  } else {
    console.log(`   ⚠️ ${file} - NO USA CONTEXTOS DE AUTH/SUBSCRIPTION`);
  }
});

// Resumen final
console.log('\n' + '='.repeat(50));
if (allFirebaseFilesRemoved) {
  console.log('🎉 ¡MIGRACIÓN COMPLETADA EXITOSAMENTE!');
  console.log('✅ Todos los archivos Firebase eliminados');
  console.log('✅ Configuración de Supabase correcta');
  console.log('✅ Contextos actualizados');
  console.log('✅ Screens usando Supabase');
  console.log('');
  console.log('🚀 LA APP ANDROID ESTÁ 100% MIGRADA A SUPABASE!');
  console.log('');
  console.log('📋 Próximos pasos:');
  console.log('1. npm start - Iniciar la aplicación');
  console.log('2. Probar autenticación');
  console.log('3. Verificar funcionalidades');
} else {
  console.log('⚠️ MIGRACIÓN INCOMPLETA');
  console.log('❌ Algunos elementos de Firebase aún presentes');
  console.log('📝 Revisar los elementos marcados con ❌ arriba');
}
console.log('='.repeat(50));
