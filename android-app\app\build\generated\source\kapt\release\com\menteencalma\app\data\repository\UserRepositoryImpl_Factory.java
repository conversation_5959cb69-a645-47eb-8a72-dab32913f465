package com.menteencalma.app.data.repository;

import com.google.firebase.firestore.FirebaseFirestore;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserRepositoryImpl_Factory implements Factory<UserRepositoryImpl> {
  private final Provider<FirebaseFirestore> firestoreProvider;

  public UserRepositoryImpl_Factory(Provider<FirebaseFirestore> firestoreProvider) {
    this.firestoreProvider = firestoreProvider;
  }

  @Override
  public UserRepositoryImpl get() {
    return newInstance(firestoreProvider.get());
  }

  public static UserRepositoryImpl_Factory create(Provider<FirebaseFirestore> firestoreProvider) {
    return new UserRepositoryImpl_Factory(firestoreProvider);
  }

  public static UserRepositoryImpl newInstance(FirebaseFirestore firestore) {
    return new UserRepositoryImpl(firestore);
  }
}
