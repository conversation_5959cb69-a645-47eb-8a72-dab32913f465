package com.menteencalma.app.di;

import com.menteencalma.app.data.migration.DatabaseMigrationService;
import com.menteencalma.app.domain.repository.DatabaseRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideDatabaseMigrationServiceFactory implements Factory<DatabaseMigrationService> {
  private final Provider<DatabaseRepository> databaseRepositoryProvider;

  public AppModule_ProvideDatabaseMigrationServiceFactory(
      Provider<DatabaseRepository> databaseRepositoryProvider) {
    this.databaseRepositoryProvider = databaseRepositoryProvider;
  }

  @Override
  public DatabaseMigrationService get() {
    return provideDatabaseMigrationService(databaseRepositoryProvider.get());
  }

  public static AppModule_ProvideDatabaseMigrationServiceFactory create(
      Provider<DatabaseRepository> databaseRepositoryProvider) {
    return new AppModule_ProvideDatabaseMigrationServiceFactory(databaseRepositoryProvider);
  }

  public static DatabaseMigrationService provideDatabaseMigrationService(
      DatabaseRepository databaseRepository) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideDatabaseMigrationService(databaseRepository));
  }
}
