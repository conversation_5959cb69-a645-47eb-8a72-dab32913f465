/**
 * Componente de Chat mejorado con UX/UI superior
 * Implementa las mejoras recomendadas en el análisis UX
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/contexts/supabase-auth-context';
import { useSupabaseChat } from '@/hooks/use-supabase-chat';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card } from '@/components/ui/card';
import { 
  Send, 
  Loader2, 
  Mic, 
  MoreVertical,
  ThumbsUp,
  ThumbsDown,
  Copy,
  Sparkles
} from 'lucide-react';
import { format, isToday, isYesterday } from 'date-fns';
import { es } from 'date-fns/locale';
import { cn } from '@/lib/utils';

// Typing indicator component
const TypingIndicator = () => (
  <motion.div 
    initial={{ opacity: 0, y: 10 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -10 }}
    className="flex items-center gap-2 p-4 ml-16"
  >
    <Avatar className="h-8 w-8">
      <AvatarImage src="/images/avatar-terapeuta-mujer.png" />
      <AvatarFallback>A</AvatarFallback>
    </Avatar>
    <div className="bg-card/50 rounded-2xl px-4 py-3 backdrop-blur-sm border">
      <div className="flex gap-1">
        {[0, 1, 2].map((i) => (
          <motion.div
            key={i}
            className="w-2 h-2 bg-muted-foreground/60 rounded-full"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: i * 0.2,
            }}
          />
        ))}
      </div>
    </div>
  </motion.div>
);

// Enhanced message bubble
interface MessageBubbleProps {
  message: {
    id: string;
    content: string;
    role: 'user' | 'assistant';
    timestamp: Date;
    status?: 'sending' | 'sent' | 'delivered' | 'failed';
  };
  onReaction?: (messageId: string, reaction: 'up' | 'down') => void;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message, onReaction }) => {
  const isUser = message.role === 'user';
  const [showActions, setShowActions] = useState(false);

  const formatMessageDate = (date: Date) => {
    if (isToday(date)) {
      return format(date, 'HH:mm', { locale: es });
    } else if (isYesterday(date)) {
      return `Ayer ${format(date, 'HH:mm', { locale: es })}`;
    }
    return format(date, 'dd/MM HH:mm', { locale: es });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={cn(
        "group relative flex gap-3 mb-4 transition-all duration-200",
        isUser ? "justify-end" : "justify-start"
      )}
      onHoverStart={() => setShowActions(true)}
      onHoverEnd={() => setShowActions(false)}
    >
      {/* Avatar (solo para assistant) */}
      {!isUser && (
        <Avatar className="h-10 w-10 border-2 border-primary/20 shadow-lg">
          <AvatarImage src="/images/avatar-terapeuta-mujer.png" />
          <AvatarFallback className="bg-primary/10 text-primary font-semibold">
            A
          </AvatarFallback>
        </Avatar>
      )}

      {/* Message content */}
      <div className={cn(
        "max-w-[70%] space-y-1",
        isUser ? "order-2" : "order-1"
      )}>
        {/* Message bubble */}
        <div
          className={cn(
            "relative rounded-2xl px-4 py-3 shadow-sm transition-all duration-200",
            "group-hover:shadow-md",
            isUser
              ? "bg-gradient-to-br from-primary to-primary/90 text-primary-foreground rounded-br-md"
              : "bg-card border rounded-bl-md hover:bg-card/80 backdrop-blur-sm"
          )}
        >
          {/* AI indicator */}
          {!isUser && (
            <div className="flex items-center gap-2 mb-2 opacity-70">
              <Sparkles className="h-3 w-3" />
              <span className="text-xs font-medium">Aurora - Psicóloga IA</span>
            </div>
          )}

          {/* Message text */}
          <p className="text-sm leading-relaxed whitespace-pre-wrap">
            {message.content}
          </p>

          {/* Status indicator for user messages */}
          {isUser && message.status && (
            <div className="flex justify-end mt-2">
              {message.status === 'sending' && (
                <Loader2 className="h-3 w-3 animate-spin opacity-60" />
              )}
              {message.status === 'failed' && (
                <span className="text-xs opacity-60">Error</span>
              )}
            </div>
          )}
        </div>

        {/* Timestamp */}
        <div className={cn(
          "flex items-center gap-2 px-2",
          isUser ? "justify-end" : "justify-start"
        )}>
          <span className="text-xs text-muted-foreground">
            {formatMessageDate(message.timestamp)}
          </span>
        </div>
      </div>

      {/* Action buttons */}
      <AnimatePresence>
        {showActions && !isUser && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute -right-2 top-0 flex flex-col gap-1 bg-background/95 backdrop-blur-sm rounded-lg border p-1 shadow-lg"
          >
            <Button
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={() => onReaction?.(message.id, 'up')}
            >
              <ThumbsUp className="h-3 w-3" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={() => onReaction?.(message.id, 'down')}
            >
              <ThumbsDown className="h-3 w-3" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="h-8 w-8 p-0"
              onClick={() => navigator.clipboard.writeText(message.content)}
            >
              <Copy className="h-3 w-3" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* User avatar */}
      {isUser && (
        <Avatar className="h-10 w-10 border-2 border-primary/20 shadow-lg order-3">
          <AvatarFallback className="bg-primary/10 text-primary font-semibold">
            U
          </AvatarFallback>
        </Avatar>
      )}
    </motion.div>
  );
};

// Enhanced input component
interface ChatInputProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

const ChatInput: React.FC<ChatInputProps> = ({ 
  onSendMessage, 
  disabled = false, 
  placeholder = "Escribe tu mensaje..."
}) => {
  const [input, setInput] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !disabled) {
      onSendMessage(input.trim());
      setInput('');
    }
  };

  const handleVoiceInput = () => {
    setIsRecording(!isRecording);
    // Aquí iría la lógica de reconocimiento de voz
  };

  return (
    <Card className="border-t bg-background/95 backdrop-blur-sm">
      <form onSubmit={handleSubmit} className="flex items-end gap-3 p-4">
        {/* Voice input button */}
        <Button
          type="button"
          size="sm"
          variant={isRecording ? "default" : "outline"}
          className={cn(
            "h-10 w-10 p-0 transition-all duration-200",
            isRecording && "animate-pulse"
          )}
          onClick={handleVoiceInput}
        >
          <Mic className="h-4 w-4" />
        </Button>

        {/* Text input */}
        <div className="flex-1 relative">
          <Input
            ref={inputRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={placeholder}
            disabled={disabled}
            className="pr-12 min-h-10 resize-none border-primary/20 focus:border-primary/40 rounded-xl"
            maxLength={500}
          />
          
          {/* Character counter */}
          <div className="absolute bottom-2 right-3 text-xs text-muted-foreground">
            {input.length}/500
          </div>
        </div>

        {/* Send button */}
        <Button
          type="submit"
          size="sm"
          disabled={!input.trim() || disabled}
          className={cn(
            "h-10 w-10 p-0 rounded-xl transition-all duration-200",
            "hover:scale-105 active:scale-95",
            !input.trim() && "opacity-50"
          )}
        >
          {disabled ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Send className="h-4 w-4" />
          )}
        </Button>
      </form>
    </Card>
  );
};

// Suggestion chips
const SuggestionChips = ({ suggestions, onSelect }: {
  suggestions: string[];
  onSelect: (suggestion: string) => void;
}) => (
  <div className="flex flex-wrap gap-2 p-4">
    {suggestions.map((suggestion, index) => (
      <motion.button
        key={`suggestion-${index}-${suggestion.slice(0, 10)}`}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
        onClick={() => onSelect(suggestion)}
        className="px-3 py-2 text-xs bg-muted/50 hover:bg-muted rounded-full border transition-all duration-200 hover:scale-105"
      >
        {suggestion}
      </motion.button>
    ))}
  </div>
);

// Main enhanced chat component
export default function EnhancedChatPage() {
  const { userProfile } = useAuth();
  const { messages, isLoading, sendMessage } = useSupabaseChat();
  const [isTyping, setIsTyping] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Suggested responses
  const suggestions = [
    "¿Cómo puedo manejar el estrés?",
    "Me siento ansioso últimamente",
    "¿Qué técnicas de relajación recomiendas?",
    "Tengo problemas para dormir"
  ];

  // Auto-scroll effect
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTo({
        top: scrollAreaRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  }, [messages, isTyping]);

  const handleSendMessage = async (message: string) => {
    setIsTyping(true);
    try {
      await sendMessage(message, {
        profile: {
          fullName: userProfile?.fullName,
          age: userProfile?.age,
          gender: userProfile?.gender,
          therapistGender: userProfile?.preferredTherapist === 'alejandro' ? 'male' : 'female',
          mentalHealthHistory: userProfile?.previousTherapy,
          preferences: userProfile?.communicationStyle,
          currentChallenges: userProfile?.mainConcerns ?? []
        }
      });
    } finally {
      setIsTyping(false);
    }
  };

  const handleReaction = (messageId: string, reaction: 'up' | 'down') => {
    // Implementar lógica de reacciones
    console.log(`Reaction ${reaction} for message ${messageId}`);
  };

  return (
    <div className="flex flex-col h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Header */}
      <Card className="border-b rounded-none bg-background/95 backdrop-blur-sm">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10 border-2 border-primary/20">
              <AvatarImage src="/images/avatar-terapeuta-mujer.png" />
              <AvatarFallback>A</AvatarFallback>
            </Avatar>
            <div>
              <h1 className="text-lg font-semibold">Aurora</h1>
              <p className="text-sm text-muted-foreground">Psicóloga Virtual</p>
            </div>
          </div>
          <Button size="sm" variant="ghost">
            <MoreVertical className="h-4 w-4" />
          </Button>
        </div>
      </Card>

      {/* Messages */}
      <ScrollArea ref={scrollAreaRef} className="flex-1 p-4">
        <div className="max-w-4xl mx-auto space-y-1">
          <AnimatePresence>
            {messages.map((message) => (
              <MessageBubble
                key={message.id}
                message={{
                  id: message.id,
                  content: message.content,
                  role: message.sender === 'user' ? 'user' : 'assistant',
                  timestamp: new Date(message.created_at),
                  status: 'sent'
                }}
                onReaction={handleReaction}
              />
            ))}
          </AnimatePresence>
          
          {/* Typing indicator */}
          {isTyping && <TypingIndicator />}
        </div>
      </ScrollArea>

      {/* Suggestions (solo si no hay mensajes) */}
      {messages.length === 0 && (
        <SuggestionChips
          suggestions={suggestions}
          onSelect={handleSendMessage}
        />
      )}

      {/* Input */}
      <ChatInput
        onSendMessage={handleSendMessage}
        disabled={isLoading}
        placeholder="¿En qué puedo ayudarte hoy?"
      />
    </div>
  );
}
