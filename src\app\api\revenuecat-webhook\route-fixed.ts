import { NextResponse } from 'next/server';
import { supabaseServer } from '@/lib/supabase-server';

// Helper function to get platform from store
function getPlatformFromStore(store: string): string {
  if (store === 'STRIPE') return 'web';
  if (store === 'PLAY_STORE') return 'android';
  return 'ios';
}

// Helper function to get plan type from product ID
function getPlanType(productId: string): string {
  const isAnnual = productId.includes('annual') || productId.includes('year');
  return isAnnual ? 'annual' : 'monthly';
}

// Helper function to get expiration date
function getExpirationDate(premiumEntitlement: any, event: any): string | null {
  if (premiumEntitlement?.expires_date) {
    return new Date(premiumEntitlement.expires_date).toISOString();
  }
  if (event.expiration_date_ms) {
    return new Date(event.expiration_date_ms).toISOString();
  }
  return null;
}

// Helper function to handle subscription activation
async function handleSubscriptionActivation(event: any, premiumEntitlement: any) {
  const supabase = supabaseServer;
  const productId = event.product_id ?? '';
  const planType = getPlanType(productId);
  const platform = getPlatformFromStore(event.store);
  const expirationDate = getExpirationDate(premiumEntitlement, event);

  await supabase
    .from('profiles')
    .update({
      subscriptionStatus: 'premium',
      subscriptionPlatform: platform,
      subscriptionPlanType: planType,
      subscriptionExpiresAt: expirationDate
    })
    .eq('uid', event.app_user_id);

  console.log('✅ Subscription activated for user:', event.app_user_id, 'Platform:', platform);
}

// Helper function to handle subscription expiration
async function handleSubscriptionExpiration(userId: string) {
  const supabase = supabaseServer;

  await supabase
    .from('profiles')
    .update({
      subscriptionStatus: 'free',
      subscriptionPlatform: null,
      subscriptionPlanType: 'free'
    })
    .eq('uid', userId);

  console.log('❌ Subscription expired/billing issue for user:', userId);
}

// Helper function to handle subscription reactivation
async function handleSubscriptionReactivation(event: any, premiumEntitlement: any) {
  const supabase = supabaseServer;
  const productId = event.product_id ?? '';
  const planType = getPlanType(productId);
  const platform = getPlatformFromStore(event.store);
  const expirationDate = event.expiration_date_ms
    ? new Date(event.expiration_date_ms).toISOString()
    : null;

  await supabase
    .from('profiles')
    .update({
      subscriptionStatus: 'premium',
      subscriptionPlatform: platform,
      subscriptionPlanType: planType,
      subscriptionExpiresAt: expirationDate
    })
    .eq('uid', event.app_user_id);

  console.log('✅ Subscription reactivated for user:', event.app_user_id);
}

export async function POST(req: Request) {
  try {
    const event = await req.json();
    
    console.log('🔔 RevenueCat webhook received:', {
      type: event.type,
      appUserId: event.app_user_id,
      store: event.store
    });

    // Verificar que tenemos los datos necesarios
    if (!event.app_user_id) {
      console.error('❌ No app_user_id in webhook');
      return NextResponse.json({ error: 'Missing app_user_id' }, { status: 400 });
    }

    // Verificar entitlements específicos de RevenueCat
    const entitlements = event.entitlements ?? {};
    const premiumEntitlement = entitlements['premium_access']; // Usar el entitlement real

    switch (event.type) {
      case 'INITIAL_PURCHASE':
      case 'RENEWAL':
      case 'NON_RENEWING_PURCHASE':
        await handleSubscriptionActivation(event, premiumEntitlement);
        break;

      case 'CANCELLATION':
        // No cambiar el estado inmediatamente en cancelación
        // El usuario mantiene acceso hasta que expire
        console.log('⚠️ Subscription cancelled (but still active until expiry):', event.app_user_id);
        break;

      case 'EXPIRATION':
      case 'BILLING_ISSUE':
        await handleSubscriptionExpiration(event.app_user_id);
        break;

      case 'UNCANCELLATION':
        await handleSubscriptionReactivation(event, premiumEntitlement);
        break;

      default:
        console.log('ℹ️ Unhandled webhook event type:', event.type);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('❌ Error processing RevenueCat webhook:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}
