package com.menteencalma.app.presentation.viewmodels;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SubscriptionViewModel_Factory implements Factory<SubscriptionViewModel> {
  @Override
  public SubscriptionViewModel get() {
    return newInstance();
  }

  public static SubscriptionViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static SubscriptionViewModel newInstance() {
    return new SubscriptionViewModel();
  }

  private static final class InstanceHolder {
    private static final SubscriptionViewModel_Factory INSTANCE = new SubscriptionViewModel_Factory();
  }
}
